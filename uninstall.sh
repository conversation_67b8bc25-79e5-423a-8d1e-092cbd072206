#!/bin/bash

# DJI设备监控服务卸载脚本

set -e

echo "=== DJI设备监控服务卸载程序 ==="
echo ""

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请以root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

echo "1. 停止服务..."

# 停止服务
systemctl stop dji-monitor.service 2>/dev/null || true

echo "2. 禁用服务..."

# 禁用服务
systemctl disable dji-monitor.service 2>/dev/null || true

echo "3. 删除服务文件..."

# 删除systemd服务文件
rm -f /etc/systemd/system/dji-monitor.service

# 重新加载systemd配置
systemctl daemon-reload

echo "4. 删除程序文件..."

# 删除服务目录
rm -rf /opt/dji_monitor

echo "5. 清理日志文件..."

# 删除日志文件
rm -f /var/log/dji_monitor.log

echo "6. 清理临时文件..."

# 清理临时挂载记录
rm -f /tmp/dji_mounts.log

# 清理可能残留的挂载点
find /media -name "dji_device_*" -type d -empty -delete 2>/dev/null || true

echo ""
echo "✅ DJI设备监控服务卸载完成!"
echo ""
echo "注意: Python依赖包(pyudev)和系统工具未被删除"
echo "如需完全清理，可手动执行:"
echo "  sudo pip3 uninstall pyudev"
echo ""
echo "=== 卸载完成 ==="

