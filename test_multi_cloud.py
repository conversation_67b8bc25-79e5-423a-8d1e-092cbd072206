#!/usr/bin/env python3
"""
多云存储功能测试脚本
测试七牛云和阿里云OSS的配置切换和上传功能
"""

import os
import sys
import tempfile
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from cloud_storage_config import *
    print("✅ 云存储配置文件加载成功")
    print(f"当前云存储提供商: {CLOUD_STORAGE_PROVIDER}")
    print(f"云存储上传状态: {'已启用' if CLOUD_UPLOAD_ENABLED else '已禁用'}")
except ImportError as e:
    print(f"❌ 云存储配置文件加载失败: {e}")
    sys.exit(1)

def create_test_files(test_dir):
    """创建测试文件"""
    print(f"\n创建测试文件到: {test_dir}")
    
    # 创建子目录结构
    subdirs = ['DCIM', 'MISC', 'LOG']
    for subdir in subdirs:
        subdir_path = os.path.join(test_dir, subdir)
        os.makedirs(subdir_path, exist_ok=True)
        
        # 在每个子目录中创建测试文件
        for i in range(3):
            file_path = os.path.join(subdir_path, f"test_file_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(f"测试文件内容 - {subdir} - {i}\n")
                f.write(f"创建时间: {datetime.now()}\n")
                f.write("这是一个用于测试云存储上传功能的文件。\n" * 10)
    
    print(f"测试文件创建完成")

def count_files(directory):
    """统计目录中的文件数量"""
    count = 0
    for root, dirs, files in os.walk(directory):
        count += len(files)
    return count

def test_cloud_storage_provider(provider):
    """测试指定的云存储提供商"""
    print(f"\n{'='*60}")
    print(f"测试云存储提供商: {provider}")
    print(f"{'='*60}")
    
    try:
        # 切换云存储提供商
        switch_cloud_provider(provider)
        print(f"✅ 成功切换到 {provider}")
        
        # 获取当前配置
        current_config = get_current_config()
        print(f"当前配置: {current_config['provider']}")
        
        # 创建临时测试目录
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = os.path.join(temp_dir, "test_backup")
            os.makedirs(test_dir, exist_ok=True)
            
            # 创建测试文件
            create_test_files(test_dir)
            
            # 统计文件数量
            file_count = count_files(test_dir)
            print(f"创建的文件数量: {file_count}")
            
            # 测试上传功能
            if provider == 'qiniu':
                test_qiniu_upload(test_dir)
            elif provider == 'aliyun_oss':
                test_aliyun_oss_upload(test_dir)
                
    except Exception as e:
        print(f"❌ 测试 {provider} 失败: {e}")
        import traceback
        traceback.print_exc()

def test_qiniu_upload(test_dir):
    """测试七牛云上传"""
    print(f"\n测试七牛云上传功能...")
    
    try:
        from dji_monitor import DJIDeviceMonitor
        
        # 创建监控器实例
        monitor = DJIDeviceMonitor()
        
        if not monitor.cloud_upload_enabled:
            print("❌ 七牛云上传功能未启用")
            return
            
        print(f"七牛云配置状态: {'已启用' if monitor.qiniu_enabled else '未启用'}")
        
        # 测试上传
        device_serial = f"TEST_QINIU_{int(time.time())}"
        print(f"开始上传测试，设备序列号: {device_serial}")
        
        upload_success = monitor.upload_to_cloud_storage(
            test_dir,
            device_serial,
            device_id="TEST_QINIU",
            delete_after_upload=False
        )
        
        if upload_success:
            print("✅ 七牛云上传测试成功")
        else:
            print("❌ 七牛云上传测试失败")
            
    except Exception as e:
        print(f"❌ 七牛云上传测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_aliyun_oss_upload(test_dir):
    """测试阿里云OSS上传"""
    print(f"\n测试阿里云OSS上传功能...")
    
    try:
        from dji_monitor import DJIDeviceMonitor
        
        # 创建监控器实例
        monitor = DJIDeviceMonitor()
        
        if not monitor.cloud_upload_enabled:
            print("❌ 阿里云OSS上传功能未启用")
            return
            
        print(f"阿里云OSS配置状态: {'已启用' if monitor.cloud_uploader and monitor.cloud_uploader.enabled else '未启用'}")
        
        # 测试上传
        device_serial = f"TEST_OSS_{int(time.time())}"
        print(f"开始上传测试，设备序列号: {device_serial}")
        
        upload_success = monitor.upload_to_cloud_storage(
            test_dir,
            device_serial,
            device_id="TEST_OSS",
            delete_after_upload=False
        )
        
        if upload_success:
            print("✅ 阿里云OSS上传测试成功")
        else:
            print("❌ 阿里云OSS上传测试失败")
            
    except Exception as e:
        print(f"❌ 阿里云OSS上传测试异常: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("多云存储功能测试开始")
    print(f"测试时间: {datetime.now()}")
    
    # 保存原始配置
    original_provider = CLOUD_STORAGE_PROVIDER
    
    try:
        # 测试七牛云
        test_cloud_storage_provider('qiniu')
        
        # 测试阿里云OSS
        test_cloud_storage_provider('aliyun_oss')
        
        print(f"\n{'='*60}")
        print("所有测试完成")
        print(f"{'='*60}")
        
    finally:
        # 恢复原始配置
        try:
            switch_cloud_provider(original_provider)
            print(f"\n✅ 已恢复原始云存储提供商: {original_provider}")
        except Exception as e:
            print(f"❌ 恢复原始配置失败: {e}")

if __name__ == "__main__":
    main()
