#!/usr/bin/env python3
"""
DJI设备专用监控服务（多云存储备份增强版）
专门监控DJI设备的连接和断开，自动执行挂载操作、本地备份和多云存储上传
支持七牛云和阿里云OSS
"""

import os
import sys
import time
import json
import logging
import subprocess
import threading
import uuid
import hashlib
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from threading import Lock

# 导入上报服务
try:
    from dji_report_service import get_report_service
    REPORT_SERVICE_AVAILABLE = True
except ImportError:
    print("警告: DJI上报服务未找到，上报功能将被禁用")
    REPORT_SERVICE_AVAILABLE = False

# 云存储配置导入
try:
    from cloud_storage_config import *
    CLOUD_CONFIG_AVAILABLE = True
except ImportError:
    print("警告: 云存储配置文件未找到，尝试使用旧版配置")
    CLOUD_CONFIG_AVAILABLE = False
    try:
        from qiniu_config import *
        # 设置默认值以保持兼容性
        CLOUD_STORAGE_PROVIDER = 'qiniu'
        CLOUD_UPLOAD_ENABLED = QINIU_UPLOAD_ENABLED
    except ImportError:
        print("警告: 配置文件未找到，使用默认配置")
        CLOUD_STORAGE_PROVIDER = 'qiniu'
        CLOUD_UPLOAD_ENABLED = False

# 七牛云相关导入
try:
    from qiniu import Auth, put_file, put_data, BucketManager
    from qiniu import CdnManager
    import qiniu.config
    QINIU_AVAILABLE = True
except ImportError:
    QINIU_AVAILABLE = False
    print("警告: 七牛云SDK未安装")
    if CLOUD_STORAGE_PROVIDER == 'qiniu':
        print("请运行: pip install qiniu")

# 阿里云OSS相关导入
try:
    from aliyun_oss_uploader import AliyunOSSUploader, ALIYUN_OSS_AVAILABLE
    OSS_UPLOADER_AVAILABLE = True
except ImportError:
    OSS_UPLOADER_AVAILABLE = False
    ALIYUN_OSS_AVAILABLE = False
    print("警告: 阿里云OSS上传器未找到")
    if CLOUD_STORAGE_PROVIDER == 'aliyun_oss':
        print("请确保 aliyun_oss_uploader.py 文件存在并安装: pip install oss2")

try:
    import pyudev
except ImportError:
    print("错误: 需要安装pyudev库")
    print("请运行: sudo pip3 install pyudev")
    sys.exit(1)

class DJIDeviceMonitor:
    """DJI设备专用监控器（多设备并发支持版）"""
    
    def __init__(self, log_file="/var/log/dji_monitor.log"):
        self.log_file = log_file
        self.setup_logging()
        self.context = pyudev.Context()
        self.monitor = pyudev.Monitor.from_netlink(self.context)
        self.monitor.filter_by(subsystem='block')
        
        # 多设备并发支持
        self.dji_devices = {}  # 存储已检测到的DJI设备
        self.device_lock = Lock()  # 设备字典访问锁
        self.mount_lock = Lock()   # 挂载操作锁
        self.backup_lock = Lock()  # 备份操作锁
        self.upload_lock = Lock()  # 上传操作锁
        self.executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="DJI-Worker")
        self.upload_executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="QiNiu-Upload")
        
        # DJI设备识别信息
        self.dji_vendor_ids = ['2ca3']  # DJI Technology Co., Ltd.
        self.dji_keywords = ['dji', 'DJI', 'mavic', 'phantom', 'inspire', 'osmo']
        
        # 云存储配置初始化
        self.cloud_provider = CLOUD_STORAGE_PROVIDER
        self.cloud_upload_enabled = CLOUD_UPLOAD_ENABLED
        self.cloud_uploader = None

        # 初始化云存储上传器
        self._init_cloud_storage()

        # 初始化上报服务
        self.report_service = None
        if REPORT_SERVICE_AVAILABLE:
            try:
                self.report_service = get_report_service()
                self.logger.info("DJI设备状态上报服务已启用")
            except PermissionError as e:
                # 权限不足时尝试使用临时日志文件
                try:
                    from dji_report_service import DJIReportService
                    self.report_service = DJIReportService(log_file="/tmp/dji_report.log")
                    self.logger.warning(f"上报服务使用临时日志文件: /tmp/dji_report.log")
                    self.logger.info("DJI设备状态上报服务已启用（临时模式）")
                except Exception as e2:
                    self.logger.warning(f"初始化上报服务失败（临时模式）: {e2}")
                    self.report_service = None
            except Exception as e:
                self.logger.warning(f"初始化上报服务失败: {e}")
                self.report_service = None
        else:
            self.logger.warning("DJI设备状态上报服务未启用")

        self.logger.info(f"DJI设备监控服务启动（多云存储备份增强版）")
        self.logger.info(f"当前云存储提供商: {self.cloud_provider}")
        self.logger.info(f"云存储上传状态: {'已启用' if self.cloud_upload_enabled else '已禁用'}")
        self.logger.info(f"监控DJI厂商ID: {self.dji_vendor_ids}")
        self.logger.info(f"最大并发处理设备数: 5")
        self.logger.info(f"最大并发上传数: 3")
        
    def setup_logging(self):
        """设置日志"""
        # 创建日志目录
        log_dir = os.path.dirname(self.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def _init_cloud_storage(self):
        """初始化云存储上传器"""
        try:
            if not self.cloud_upload_enabled:
                self.logger.warning("云存储上传功能已禁用")
                return

            if self.cloud_provider == 'qiniu':
                self._init_qiniu_storage()
            elif self.cloud_provider == 'aliyun_oss':
                self._init_aliyun_oss_storage()
            else:
                self.logger.error(f"不支持的云存储提供商: {self.cloud_provider}")
                self.cloud_upload_enabled = False

        except Exception as e:
            self.logger.error(f"初始化云存储失败: {e}")
            self.cloud_upload_enabled = False

    def _init_qiniu_storage(self):
        """初始化七牛云存储"""
        if not QINIU_AVAILABLE:
            self.logger.error("七牛云SDK不可用")
            self.cloud_upload_enabled = False
            return

        try:
            # 获取七牛云配置
            if CLOUD_CONFIG_AVAILABLE:
                config = QINIU_CONFIG
            else:
                # 兼容旧配置
                config = {
                    'access_key': QINIU_ACCESS_KEY,
                    'secret_key': QINIU_SECRET_KEY,
                    'bucket_name': QINIU_BUCKET_NAME,
                    'upload_timeout': QINIU_UPLOAD_TIMEOUT,
                }

            self.qiniu_auth = Auth(config['access_key'], config['secret_key'])
            self.qiniu_bucket = config['bucket_name']
            self.upload_stats = {
                'total_files': 0,
                'uploaded_files': 0,
                'failed_files': 0,
                'total_size': 0,
                'uploaded_size': 0
            }

            # 保持向后兼容
            self.qiniu_enabled = True

            self.logger.info(f"七牛云存储初始化成功，目标空间: {self.qiniu_bucket}")

        except Exception as e:
            self.logger.error(f"初始化七牛云存储失败: {e}")
            self.cloud_upload_enabled = False

    def _init_aliyun_oss_storage(self):
        """初始化阿里云OSS存储"""
        if not OSS_UPLOADER_AVAILABLE or not ALIYUN_OSS_AVAILABLE:
            self.logger.error("阿里云OSS SDK或上传器不可用")
            self.cloud_upload_enabled = False
            return

        try:
            # 获取阿里云OSS配置
            config = ALIYUN_OSS_CONFIG

            # 创建OSS上传器
            self.cloud_uploader = AliyunOSSUploader(config, self.logger)

            if not self.cloud_uploader.enabled:
                self.logger.error("阿里云OSS上传器初始化失败")
                self.cloud_upload_enabled = False
                return

            # 保持向后兼容
            self.qiniu_enabled = False

            self.logger.info(f"阿里云OSS存储初始化成功，目标存储桶: {config['bucket_name']}")

        except Exception as e:
            self.logger.error(f"初始化阿里云OSS存储失败: {e}")
            self.cloud_upload_enabled = False
        
    def is_dji_device(self, device):
        """检查是否为DJI设备"""
        try:
            # 检查设备路径
            device_path = device.device_node
            if not device_path or not device_path.startswith('/dev/sd'):
                return False
                
            # 检查是否为块设备
            if device.device_type != 'disk' and device.device_type != 'partition':
                return False
                
            # 通过USB信息检查DJI设备
            usb_info = self.get_usb_device_info(device_path)
            if usb_info and any(vid in usb_info.get('vendor_id', '') for vid in self.dji_vendor_ids):
                return True
                
            # 通过设备属性检查
            vendor = device.get('ID_VENDOR', '').lower()
            model = device.get('ID_MODEL', '').lower()
            serial = device.get('ID_SERIAL', '').lower()
            
            # 检查是否包含DJI关键词
            device_info = f"{vendor} {model} {serial}"
            if any(keyword.lower() in device_info for keyword in self.dji_keywords):
                return True
                
            # 检查父设备的USB信息
            parent = device.parent
            while parent:
                if parent.subsystem == 'usb':
                    usb_vendor = parent.get('ID_VENDOR_ID', '')
                    if usb_vendor in self.dji_vendor_ids:
                        return True
                parent = parent.parent
                
            return False
            
        except Exception as e:
            self.logger.error(f"检查DJI设备时出错: {e}")
            return False
            
    def get_dji_device_serial(self, device_path):
        """获取DJI设备序列号"""
        try:
            # 使用lsusb获取DJI设备的完整信息
            result = subprocess.run(['lsusb'], capture_output=True, text=True)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if any(vid in line for vid in self.dji_vendor_ids):
                        # 解析lsusb输出格式: Bus 003 Device 010: ID 2ca3:0020 DJI Technology Co., Ltd. DJI-1581F87L524CU00SZ0K7
                        parts = line.split()
                        if len(parts) >= 6:
                            # 查找最后一个包含DJI-的部分作为序列号
                            for part in reversed(parts):
                                if part.startswith('DJI-') and len(part) > 4:
                                    self.logger.info(f"提取到DJI设备序列号: {part}")
                                    return part
                            
                            # 如果没找到DJI-开头的，尝试查找其他可能的序列号格式
                            description = ' '.join(parts[6:]) if len(parts) > 6 else ''
                            if description:
                                # 查找可能的序列号模式
                                import re
                                serial_pattern = r'[A-Z0-9]{10,}'  # 至少10个字符的大写字母数字组合
                                matches = re.findall(serial_pattern, description)
                                if matches:
                                    serial = matches[-1]  # 取最后一个匹配项
                                    self.logger.info(f"提取到设备序列号: {serial}")
                                    return serial
                                    
            # 如果lsusb无法获取，尝试从设备属性获取
            try:
                result = subprocess.run(['udevadm', 'info', '--query=all', f'--name={device_path}'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'ID_SERIAL' in line and '=' in line:
                            serial = line.split('=')[1].strip()
                            if serial and len(serial) > 5:
                                self.logger.info(f"从设备属性获取序列号: {serial}")
                                return serial
            except Exception as e:
                self.logger.debug(f"从设备属性获取序列号失败: {e}")
                
            # 如果都无法获取，使用设备路径和时间戳生成
            import time
            fallback_serial = f"DJI_DEVICE_{int(time.time())}"
            self.logger.warning(f"无法获取设备序列号，使用备用名称: {fallback_serial}")
            return fallback_serial
            
        except Exception as e:
            self.logger.error(f"获取DJI设备序列号失败: {e}")
            import time
            fallback_serial = f"DJI_DEVICE_{int(time.time())}"
            return fallback_serial
            
    def get_usb_device_info(self, device_path):
        """获取USB设备信息"""
        try:
            # 使用lsusb获取USB设备信息
            result = subprocess.run(['lsusb'], capture_output=True, text=True)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if any(vid in line for vid in self.dji_vendor_ids):
                        parts = line.split()
                        if len(parts) >= 6:
                            vendor_product = parts[5].split(':')
                            if len(vendor_product) == 2:
                                return {
                                    'vendor_id': vendor_product[0],
                                    'product_id': vendor_product[1],
                                    'description': ' '.join(parts[6:]) if len(parts) > 6 else '',
                                    'full_line': line  # 保存完整的lsusb输出行
                                }
            return None
        except Exception as e:
            self.logger.error(f"获取USB设备信息失败: {e}")
            return None
            
    def execute_mount_command(self, command):
        """执行挂载命令"""
        try:
            self.logger.info(f"执行命令: {command}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.logger.info(f"命令执行成功: {command}")
                return True, result.stdout
            else:
                self.logger.warning(f"命令执行失败: {command}, 错误: {result.stderr}")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"命令执行超时: {command}")
            return False, "命令执行超时"
        except Exception as e:
            self.logger.error(f"执行命令异常: {command}, 错误: {e}")
            return False, str(e)
            
    def generate_unique_mount_point(self, device_path):
        """生成唯一的挂载点"""
        try:
            # 使用设备路径和时间戳生成唯一标识
            device_hash = hashlib.md5(device_path.encode()).hexdigest()[:8]
            timestamp = int(time.time() * 1000000)  # 微秒级时间戳
            unique_id = f"{device_hash}_{timestamp}"
            
            mount_point = f"/media/dji_device_{unique_id}"
            
            # 确保挂载点不存在
            counter = 0
            original_mount_point = mount_point
            while os.path.exists(mount_point):
                counter += 1
                mount_point = f"{original_mount_point}_{counter}"
                
            return mount_point
            
        except Exception as e:
            self.logger.error(f"生成唯一挂载点失败: {e}")
            # 备用方案：使用UUID
            unique_id = str(uuid.uuid4())[:8]
            return f"/media/dji_device_{unique_id}"
            
    def generate_unique_backup_dir(self, device_serial, backup_home):
        """生成唯一的备份目录"""
        try:
            backup_dir = os.path.join(backup_home, device_serial)
            
            # 如果目录已存在，添加时间戳和随机标识
            if os.path.exists(backup_dir):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                random_id = str(uuid.uuid4())[:8]
                backup_dir = os.path.join(backup_home, f"{device_serial}_{timestamp}_{random_id}")
                
            # 再次检查，确保唯一性
            counter = 0
            original_backup_dir = backup_dir
            while os.path.exists(backup_dir):
                counter += 1
                backup_dir = f"{original_backup_dir}_{counter}"
                
            return backup_dir
            
        except Exception as e:
            self.logger.error(f"生成唯一备份目录失败: {e}")
            # 备用方案：使用UUID
            unique_id = str(uuid.uuid4())[:8]
            return os.path.join(backup_home, f"{device_serial}_{unique_id}")
            
    def thread_safe_log(self, level, message, device_id=None):
        """线程安全的日志记录"""
        try:
            thread_name = threading.current_thread().name
            if device_id:
                log_message = f"[{thread_name}][{device_id}] {message}"
            else:
                log_message = f"[{thread_name}] {message}"
                
            if level == 'info':
                self.logger.info(log_message)
            elif level == 'warning':
                self.logger.warning(log_message)
            elif level == 'error':
                self.logger.error(log_message)
            elif level == 'debug':
                self.logger.debug(log_message)
                
        except Exception as e:
            # 如果日志记录失败，使用print作为备用
            print(f"日志记录失败: {e}, 原消息: {message}")
            
    def mount_dji_device(self, device_path):
        """挂载DJI设备（线程安全版）"""
        device_id = os.path.basename(device_path)
        
        with self.mount_lock:  # 挂载操作加锁
            try:
                self.thread_safe_log('info', f"开始挂载DJI设备: {device_path}", device_id)
                
                # 检查设备是否已挂载
                result = subprocess.run(['lsblk', '-o', 'NAME,MOUNTPOINT', '-n', device_path], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    for line in result.stdout.strip().split('\n'):
                        if line.strip():
                            parts = line.strip().split()
                            if len(parts) >= 2 and parts[1] != '':
                                mount_point = parts[1]
                                self.thread_safe_log('info', f"设备已挂载到: {mount_point}", device_id)
                                return mount_point
                                
                # 生成唯一挂载点
                mount_point = self.generate_unique_mount_point(device_path)
                
                success, output = self.execute_mount_command(f"mkdir -p {mount_point}")
                if not success:
                    self.thread_safe_log('error', f"创建挂载点失败: {output}", device_id)
                    return None
                    
                self.thread_safe_log('info', f"创建挂载点: {mount_point}", device_id)
                
                # DJI设备挂载策略（按优先级尝试）
                mount_strategies = [
                    f"mount {device_path} {mount_point}",
                    f"mount -t vfat {device_path} {mount_point}",
                    f"mount -t exfat -o uid=1000,gid=1000,umask=0022 {device_path} {mount_point}",
                    f"mount -t ntfs-3g -o uid=1000,gid=1000,umask=0022 {device_path} {mount_point}",
                    f"mount -o force,rw {device_path} {mount_point}",
                    f"mount -o ro {device_path} {mount_point}"
                ]
                
                # 尝试不同的挂载策略
                for strategy in mount_strategies:
                    self.thread_safe_log('info', f"尝试挂载策略: {strategy}", device_id)
                    success, output = self.execute_mount_command(strategy)
                    
                    if success:
                        self.thread_safe_log('info', f"DJI设备挂载成功: {device_path} -> {mount_point}", device_id)

                        # 记录挂载信息（线程安全）
                        mount_info = f"{device_path}:{mount_point}:{datetime.now().isoformat()}"
                        try:
                            with open('/tmp/dji_mounts.log', 'a') as f:
                                f.write(mount_info + '\n')
                        except Exception as e:
                            self.thread_safe_log('warning', f"记录挂载信息失败: {e}", device_id)

                        return mount_point
                    else:
                        self.thread_safe_log('debug', f"挂载策略失败: {strategy}, 错误: {output}", device_id)
                        
                # 所有策略都失败，清理挂载点
                self.thread_safe_log('error', f"所有挂载策略都失败，清理挂载点: {mount_point}", device_id)
                self.execute_mount_command(f"rmdir {mount_point}")
                return None
                
            except Exception as e:
                self.thread_safe_log('error', f"挂载DJI设备异常: {e}", device_id)
                return None
            
    def list_files_and_folders(self, mount_point, max_depth=3):
        """列出挂载点中的文件和文件夹"""
        try:
            self.logger.info(f"开始列出文件结构: {mount_point}")
            
            if not os.path.exists(mount_point):
                self.logger.error(f"挂载点不存在: {mount_point}")
                return
                
            if not os.path.ismount(mount_point):
                self.logger.warning(f"路径不是挂载点: {mount_point}")
                
            # 获取文件系统信息
            try:
                stat_result = os.statvfs(mount_point)
                total_size = stat_result.f_blocks * stat_result.f_frsize
                free_size = stat_result.f_bavail * stat_result.f_frsize
                used_size = total_size - free_size
                
                self.logger.info(f"文件系统信息:")
                self.logger.info(f"  总容量: {self.format_size(total_size)}")
                self.logger.info(f"  已使用: {self.format_size(used_size)}")
                self.logger.info(f"  可用空间: {self.format_size(free_size)}")
            except Exception as e:
                self.logger.warning(f"获取文件系统信息失败: {e}")
                
            # 递归列出文件和文件夹
            self.logger.info(f"文件和文件夹结构:")
            self._list_directory_recursive(mount_point, "", 0, max_depth)
            
        except Exception as e:
            self.logger.error(f"列出文件结构异常: {e}")
            
    def _list_directory_recursive(self, path, prefix, depth, max_depth):
        """递归列出目录内容"""
        if depth > max_depth:
            return
            
        try:
            items = []
            for item in os.listdir(path):
                if item.startswith('.'):  # 跳过隐藏文件
                    continue
                item_path = os.path.join(path, item)
                try:
                    stat_info = os.stat(item_path)
                    is_dir = os.path.isdir(item_path)
                    size = stat_info.st_size if not is_dir else 0
                    mtime = datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    
                    items.append({
                        'name': item,
                        'path': item_path,
                        'is_dir': is_dir,
                        'size': size,
                        'mtime': mtime
                    })
                except (OSError, PermissionError) as e:
                    self.logger.warning(f"无法访问: {item_path}, 错误: {e}")
                    
            # 排序：文件夹在前，然后按名称排序
            items.sort(key=lambda x: (not x['is_dir'], x['name'].lower()))
            
            # 打印当前目录内容
            for i, item in enumerate(items):
                is_last = (i == len(items) - 1)
                connector = "└── " if is_last else "├── "
                
                if item['is_dir']:
                    self.logger.info(f"{prefix}{connector}📁 {item['name']}/")
                    
                    # 递归进入子目录
                    if depth < max_depth:
                        new_prefix = prefix + ("    " if is_last else "│   ")
                        self._list_directory_recursive(item['path'], new_prefix, depth + 1, max_depth)
                else:
                    size_str = self.format_size(item['size'])
                    self.logger.info(f"{prefix}{connector}📄 {item['name']} ({size_str}) [{item['mtime']}]")
                    
        except PermissionError:
            self.logger.warning(f"{prefix}❌ 权限不足，无法访问目录: {path}")
        except Exception as e:
            self.logger.error(f"{prefix}❌ 列出目录内容失败: {path}, 错误: {e}")
            
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"
        
    def unmount_dji_device(self, device_path, device_id=None):
        """卸载DJI设备"""
        try:
            self.logger.info(f"开始卸载DJI设备: {device_path}")
            
            # 查找挂载点
            result = subprocess.run(['lsblk', '-o', 'NAME,MOUNTPOINT', '-n', device_path], 
                                  capture_output=True, text=True)
            mount_points = []
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        parts = line.strip().split()
                        if len(parts) >= 2 and parts[1] != '':
                            mount_points.append(parts[1])
                            
            # 从记录文件中查找挂载点
            if os.path.exists('/tmp/dji_mounts.log'):
                with open('/tmp/dji_mounts.log', 'r') as f:
                    for line in f:
                        if line.startswith(device_path + ':'):
                            parts = line.strip().split(':')
                            if len(parts) >= 2:
                                mount_points.append(parts[1])
                                
            # 去重
            mount_points = list(set(mount_points))
            
            if not mount_points:
                self.logger.info(f"设备未挂载: {device_path}")
                return True
                
            # 卸载所有挂载点
            success_count = 0
            for mount_point in mount_points:
                self.logger.info(f"卸载挂载点: {mount_point}")
                
                # 检查进程占用
                result = subprocess.run(['lsof', mount_point], capture_output=True, text=True)
                if result.returncode == 0 and result.stdout.strip():
                    self.logger.warning(f"挂载点被进程占用: {mount_point}")
                    self.logger.info("尝试强制卸载...")
                    
                # 尝试卸载
                for umount_cmd in [f"umount {mount_point}", f"umount -f {mount_point}", f"umount -l {mount_point}"]:
                    success, output = self.execute_mount_command(umount_cmd)
                    if success:
                        self.logger.info(f"卸载成功: {mount_point}")
                        success_count += 1
                        
                        # 清理挂载点目录
                        if mount_point.startswith('/media/dji_'):
                            self.execute_mount_command(f"rmdir {mount_point}")
                            
                        break
                    else:
                        self.logger.debug(f"卸载命令失败: {umount_cmd}, 错误: {output}")
                        
            # 清理记录文件
            if os.path.exists('/tmp/dji_mounts.log'):
                with open('/tmp/dji_mounts.log', 'r') as f:
                    lines = f.readlines()
                with open('/tmp/dji_mounts.log', 'w') as f:
                    for line in lines:
                        if not line.startswith(device_path + ':'):
                            f.write(line)
                            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"卸载DJI设备异常: {e}")
            return False
            
    def backup_dji_data(self, mount_point, device_serial, device_id=None):
        """备份DJI设备数据到用户目录（支持多设备并发）"""
        try:
            if device_id:
                self.thread_safe_log('info', f"开始备份DJI设备数据: {mount_point} -> ~/{device_serial}", device_id)
            else:
                self.logger.info(f"开始备份DJI设备数据: {mount_point} -> ~/{device_serial}")
            
            # 获取用户主目录
            import os
            import pwd
            
            # 尝试获取实际用户（非root）
            backup_user = None
            backup_home = None
            
            if device_id:
                self.thread_safe_log('info', "正在确定备份用户和目录...", device_id)
            else:
                self.logger.info("正在确定备份用户和目录...")
            
            # 检查环境变量中的用户信息
            if 'SUDO_USER' in os.environ:
                backup_user = os.environ['SUDO_USER']
                try:
                    user_info = pwd.getpwnam(backup_user)
                    backup_home = user_info.pw_dir
                    if device_id:
                        self.thread_safe_log('info', f"使用SUDO_USER: {backup_user}", device_id)
                    else:
                        self.logger.info(f"使用SUDO_USER: {backup_user}")
                except KeyError:
                    if device_id:
                        self.thread_safe_log('warning', f"SUDO_USER {backup_user} 不存在", device_id)
                    else:
                        self.logger.warning(f"SUDO_USER {backup_user} 不存在")
            
            # 如果没有找到SUDO_USER，使用uid 1000的用户
            if not backup_home:
                try:
                    user_info = pwd.getpwuid(1000)
                    backup_user = user_info.pw_name
                    backup_home = user_info.pw_dir
                    if device_id:
                        self.thread_safe_log('info', f"使用UID 1000用户: {backup_user}", device_id)
                    else:
                        self.logger.info(f"使用UID 1000用户: {backup_user}")
                except KeyError:
                    if device_id:
                        self.thread_safe_log('warning', "UID 1000用户不存在", device_id)
                    else:
                        self.logger.warning("UID 1000用户不存在")
            
            # 最后备选方案：使用/home/<USER>
            if not backup_home:
                backup_user = 'ubuntu'
                backup_home = '/home/<USER>'
                if device_id:
                    self.thread_safe_log('info', f"使用默认用户: {backup_user}", device_id)
                else:
                    self.logger.info(f"使用默认用户: {backup_user}")
                
            if device_id:
                self.thread_safe_log('info', f"备份用户: {backup_user}, 主目录: {backup_home}", device_id)
            else:
                self.logger.info(f"备份用户: {backup_user}, 主目录: {backup_home}")
            
            # 生成唯一的备份目录
            backup_dir = self.generate_unique_backup_dir(device_serial, backup_home)
                
            if device_id:
                self.thread_safe_log('info', f"创建备份目录: {backup_dir}", device_id)
            else:
                self.logger.info(f"创建备份目录: {backup_dir}")
                
            success, output = self.execute_mount_command(f"mkdir -p '{backup_dir}'")
            if not success:
                if device_id:
                    self.thread_safe_log('error', f"创建备份目录失败: {output}", device_id)
                else:
                    self.logger.error(f"创建备份目录失败: {output}")
                return False
                
            # 复制所有数据
            if device_id:
                self.thread_safe_log('info', "开始复制设备数据...", device_id)
            else:
                self.logger.info("开始复制设备数据...")
                
            copy_cmd = f"cp -r '{mount_point}'/* '{backup_dir}'/ 2>/dev/null || true"
            
            # 增加超时时间，因为可能有大文件
            if device_id:
                self.thread_safe_log('info', f"执行复制命令: {copy_cmd}", device_id)
            else:
                self.logger.info(f"执行复制命令: {copy_cmd}")
                
            try:
                result = subprocess.run(copy_cmd, shell=True, capture_output=True, text=True, timeout=300)  # 5分钟超时
                if device_id:
                    self.thread_safe_log('info', f"复制命令完成，返回码: {result.returncode}", device_id)
                else:
                    self.logger.info(f"复制命令完成，返回码: {result.returncode}")
                    
                if result.stderr:
                    if device_id:
                        self.thread_safe_log('debug', f"复制命令stderr: {result.stderr}", device_id)
                    else:
                        self.logger.debug(f"复制命令stderr: {result.stderr}")
            except subprocess.TimeoutExpired:
                if device_id:
                    self.thread_safe_log('error', "复制命令超时（5分钟）", device_id)
                else:
                    self.logger.error("复制命令超时（5分钟）")
                return False
            except Exception as e:
                if device_id:
                    self.thread_safe_log('error', f"复制命令异常: {e}", device_id)
                else:
                    self.logger.error(f"复制命令异常: {e}")
                import traceback
                self.logger.error(f"异常详情: {traceback.format_exc()}")
                return False
            
            # 检查复制结果
            if device_id:
                self.thread_safe_log('info', "检查复制结果...", device_id)
            else:
                self.logger.info("检查复制结果...")
                
            if os.path.exists(backup_dir):
                if device_id:
                    self.thread_safe_log('info', "备份目录存在，开始统计文件...", device_id)
                else:
                    self.logger.info("备份目录存在，开始统计文件...")
                
                # 统计复制的文件数量
                file_count = 0
                total_size = 0
                
                try:
                    for root, dirs, files in os.walk(backup_dir):
                        if device_id:
                            self.thread_safe_log('debug', f"遍历目录: {root}, 文件数: {len(files)}", device_id)
                        else:
                            self.logger.debug(f"遍历目录: {root}, 文件数: {len(files)}")
                        file_count += len(files)
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                size = os.path.getsize(file_path)
                                total_size += size
                                if device_id:
                                    self.thread_safe_log('debug', f"文件: {file}, 大小: {size}", device_id)
                                else:
                                    self.logger.debug(f"文件: {file}, 大小: {size}")
                            except (OSError, IOError) as e:
                                if device_id:
                                    self.thread_safe_log('warning', f"获取文件大小失败: {file}, 错误: {e}", device_id)
                                else:
                                    self.logger.warning(f"获取文件大小失败: {file}, 错误: {e}")
                    
                    if device_id:
                        self.thread_safe_log('info', f"文件统计完成: 文件数={file_count}, 总大小={total_size}", device_id)
                    else:
                        self.logger.info(f"文件统计完成: 文件数={file_count}, 总大小={total_size}")
                        
                except Exception as e:
                    if device_id:
                        self.thread_safe_log('error', f"文件统计异常: {e}", device_id)
                    else:
                        self.logger.error(f"文件统计异常: {e}")
                    # 即使统计失败，也认为备份成功
                    file_count = -1
                    total_size = -1
                
                # 设置目录权限
                if device_id:
                    self.thread_safe_log('info', "设置目录权限...", device_id)
                else:
                    self.logger.info("设置目录权限...")
                    
                try:
                    # 获取实际用户的UID和GID
                    if backup_user != 'root':
                        user_info = pwd.getpwnam(backup_user)
                        uid = user_info.pw_uid
                        gid = user_info.pw_gid
                        
                        chown_cmd = f"chown -R {uid}:{gid} '{backup_dir}'"
                        result = subprocess.run(chown_cmd, shell=True, capture_output=True, text=True, timeout=60)
                        
                        if result.returncode == 0:
                            if device_id:
                                self.thread_safe_log('info', f"权限设置成功: {backup_user}({uid}:{gid})", device_id)
                            else:
                                self.logger.info(f"权限设置成功: {backup_user}({uid}:{gid})")
                        else:
                            if device_id:
                                self.thread_safe_log('warning', f"权限设置失败: {result.stderr}", device_id)
                            else:
                                self.logger.warning(f"权限设置失败: {result.stderr}")
                except Exception as e:
                    if device_id:
                        self.thread_safe_log('warning', f"权限设置异常: {e}", device_id)
                    else:
                        self.logger.warning(f"权限设置异常: {e}")
                
                # 记录备份完成信息
                if device_id:
                    self.thread_safe_log('info', "数据复制完成:", device_id)
                    self.thread_safe_log('info', f"  备份目录: {backup_dir}", device_id)
                    self.thread_safe_log('info', f"  文件数量: {file_count}", device_id)
                    self.thread_safe_log('info', f"  总大小: {self.format_size(total_size)}", device_id)
                    self.thread_safe_log('info', "备份流程完成", device_id)
                else:
                    self.logger.info("数据复制完成:")
                    self.logger.info(f"  备份目录: {backup_dir}")
                    self.logger.info(f"  文件数量: {file_count}")
                    self.logger.info(f"  总大小: {self.format_size(total_size)}")
                    self.logger.info("备份流程完成")
                
                return backup_dir  # 返回实际的备份目录路径
            else:
                if device_id:
                    self.thread_safe_log('error', f"备份目录不存在: {backup_dir}", device_id)
                else:
                    self.logger.error(f"备份目录不存在: {backup_dir}")
                return False
                
        except Exception as e:
            if device_id:
                self.thread_safe_log('error', f"备份DJI设备数据异常: {e}", device_id)
            else:
                self.logger.error(f"备份DJI设备数据异常: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False
    
    def upload_to_qiniu(self, local_dir, device_serial, device_id=None, delete_after_upload=False, device_path=None):
        """上传文件到七牛云存储（独立线程）"""
        if not self.qiniu_enabled:
            if device_id:
                self.thread_safe_log('warning', "七牛云上传功能未启用", device_id)
            else:
                self.logger.warning("七牛云上传功能未启用")
            return False

        try:
            if device_id:
                self.thread_safe_log('info', f"开始上传到七牛云: {local_dir}", device_id)
            else:
                self.logger.info(f"开始上传到七牛云: {local_dir}")

            # 生成上传令牌
            token = self.qiniu_auth.upload_token(self.qiniu_bucket, None, QINIU_UPLOAD_TIMEOUT)

            # 统计需要上传的文件
            upload_files = []
            total_size = 0

            for root, dirs, files in os.walk(local_dir):
                for file in files:
                    local_file_path = os.path.join(root, file)
                    # 计算相对路径
                    rel_path = os.path.relpath(local_file_path, local_dir)
                    # 生成七牛云存储的key（路径）
                    date_str = datetime.now().strftime('%Y%m%d')
                    qiniu_key = f"{device_serial}/{date_str}/{rel_path}"

                    file_size = os.path.getsize(local_file_path)
                    upload_files.append({
                        'local_path': local_file_path,
                        'qiniu_key': qiniu_key,
                        'size': file_size
                    })
                    total_size += file_size

            if not upload_files:
                if device_id:
                    self.thread_safe_log('warning', "没有找到需要上传的文件", device_id)
                else:
                    self.logger.warning("没有找到需要上传的文件")
                return True

            if device_id:
                self.thread_safe_log('info', f"准备上传 {len(upload_files)} 个文件，总大小: {self.format_size(total_size)}", device_id)
            else:
                self.logger.info(f"准备上传 {len(upload_files)} 个文件，总大小: {self.format_size(total_size)}")

            # 更新统计信息
            with self.upload_lock:
                self.upload_stats['total_files'] += len(upload_files)
                self.upload_stats['total_size'] += total_size

            # 逐个上传文件
            success_count = 0
            failed_count = 0
            uploaded_size = 0
            successfully_uploaded_files = []  # 记录成功上传的文件路径

            for i, file_info in enumerate(upload_files, 1):
                try:
                    if device_id:
                        self.thread_safe_log('info', f"上传文件 {i}/{len(upload_files)}: {file_info['qiniu_key']}", device_id)
                    else:
                        self.logger.info(f"上传文件 {i}/{len(upload_files)}: {file_info['qiniu_key']}")

                    # 执行上传
                    ret, info = put_file(token, file_info['qiniu_key'], file_info['local_path'])

                    if info.status_code == 200:
                        success_count += 1
                        uploaded_size += file_info['size']
                        successfully_uploaded_files.append(file_info['local_path'])

                        if device_id:
                            self.thread_safe_log('debug', f"上传成功: {file_info['qiniu_key']}", device_id)
                        else:
                            self.logger.debug(f"上传成功: {file_info['qiniu_key']}")
                    else:
                        failed_count += 1
                        if device_id:
                            self.thread_safe_log('error', f"上传失败: {file_info['qiniu_key']}, 状态码: {info.status_code}", device_id)
                        else:
                            self.logger.error(f"上传失败: {file_info['qiniu_key']}, 状态码: {info.status_code}")

                        # 重试机制
                        for retry in range(QINIU_MAX_RETRIES):
                            if device_id:
                                self.thread_safe_log('info', f"重试上传 {retry+1}/{QINIU_MAX_RETRIES}: {file_info['qiniu_key']}", device_id)
                            else:
                                self.logger.info(f"重试上传 {retry+1}/{QINIU_MAX_RETRIES}: {file_info['qiniu_key']}")

                            time.sleep(QINIU_RETRY_DELAY)  # 重试间隔
                            ret, info = put_file(token, file_info['qiniu_key'], file_info['local_path'])

                            if info.status_code == 200:
                                success_count += 1
                                failed_count -= 1
                                uploaded_size += file_info['size']
                                successfully_uploaded_files.append(file_info['local_path'])

                                if device_id:
                                    self.thread_safe_log('info', f"重试上传成功: {file_info['qiniu_key']}", device_id)
                                else:
                                    self.logger.info(f"重试上传成功: {file_info['qiniu_key']}")
                                break
                        else:
                            if device_id:
                                self.thread_safe_log('error', f"重试上传失败: {file_info['qiniu_key']}", device_id)
                            else:
                                self.logger.error(f"重试上传失败: {file_info['qiniu_key']}")

                except Exception as e:
                    failed_count += 1
                    if device_id:
                        self.thread_safe_log('error', f"上传异常: {file_info['qiniu_key']}, 错误: {e}", device_id)
                    else:
                        self.logger.error(f"上传异常: {file_info['qiniu_key']}, 错误: {e}")

            # 更新统计信息
            with self.upload_lock:
                self.upload_stats['uploaded_files'] += success_count
                self.upload_stats['failed_files'] += failed_count
                self.upload_stats['uploaded_size'] += uploaded_size

            # 记录上传结果
            if device_id:
                self.thread_safe_log('info', f"七牛云上传完成: 成功 {success_count}/{len(upload_files)} 个文件", device_id)
                self.thread_safe_log('info', f"上传大小: {self.format_size(uploaded_size)}/{self.format_size(total_size)}", device_id)
            else:
                self.logger.info(f"七牛云上传完成: 成功 {success_count}/{len(upload_files)} 个文件")
                self.logger.info(f"上传大小: {self.format_size(uploaded_size)}/{self.format_size(total_size)}")

            # 上报文件上传完成事件
            if self.report_service and success_count > 0 and device_path:
                try:
                    upload_stats = {
                        'total_files': len(upload_files),
                        'uploaded_files': success_count,
                        'failed_files': failed_count,
                        'total_size': total_size,
                        'uploaded_size': uploaded_size,
                        'cloud_provider': 'qiniu'
                    }

                    additional_info = {
                        'local_backup_dir': local_dir,
                        'upload_duration': None  # 可以在后续版本中添加计时功能
                    }

                    self.report_service.report_upload_completed(
                        device_serial=device_serial,
                        device_path=device_path,
                        upload_stats=upload_stats,
                        additional_info=additional_info
                    )

                    if device_id:
                        self.thread_safe_log('info', "文件上传完成事件上报成功", device_id)
                    else:
                        self.logger.info("文件上传完成事件上报成功")

                except Exception as e:
                    if device_id:
                        self.thread_safe_log('warning', f"文件上传完成事件上报失败: {e}", device_id)
                    else:
                        self.logger.warning(f"文件上传完成事件上报失败: {e}")

            # 如果启用了上传后删除功能，删除成功上传的文件
            if delete_after_upload and successfully_uploaded_files:
                if device_id:
                    self.thread_safe_log('info', f"开始删除已成功上传的本地文件，共 {len(successfully_uploaded_files)} 个", device_id)
                else:
                    self.logger.info(f"开始删除已成功上传的本地文件，共 {len(successfully_uploaded_files)} 个")

                deleted_count = 0
                deleted_size = 0

                for file_path in successfully_uploaded_files:
                    try:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        deleted_count += 1
                        deleted_size += file_size

                        if device_id:
                            self.thread_safe_log('debug', f"删除本地文件: {file_path}", device_id)
                        else:
                            self.logger.debug(f"删除本地文件: {file_path}")

                    except Exception as e:
                        if device_id:
                            self.thread_safe_log('error', f"删除本地文件失败: {file_path}, 错误: {e}", device_id)
                        else:
                            self.logger.error(f"删除本地文件失败: {file_path}, 错误: {e}")

                if device_id:
                    self.thread_safe_log('info', f"本地文件删除完成: 成功删除 {deleted_count}/{len(successfully_uploaded_files)} 个文件", device_id)
                    self.thread_safe_log('info', f"释放空间: {self.format_size(deleted_size)}", device_id)
                else:
                    self.logger.info(f"本地文件删除完成: 成功删除 {deleted_count}/{len(successfully_uploaded_files)} 个文件")
                    self.logger.info(f"释放空间: {self.format_size(deleted_size)}")

                # 尝试删除空目录
                try:
                    self.cleanup_empty_directories(local_dir, device_id)
                except Exception as e:
                    if device_id:
                        self.thread_safe_log('warning', f"清理空目录失败: {e}", device_id)
                    else:
                        self.logger.warning(f"清理空目录失败: {e}")

            return success_count > 0

        except Exception as e:
            if device_id:
                self.thread_safe_log('error', f"七牛云上传异常: {e}", device_id)
            else:
                self.logger.error(f"七牛云上传异常: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False

    def cleanup_empty_directories(self, root_dir, device_id=None):
        """清理空目录（从叶子节点开始）"""
        try:
            if device_id:
                self.thread_safe_log('info', f"开始清理空目录: {root_dir}", device_id)
            else:
                self.logger.info(f"开始清理空目录: {root_dir}")

            # 从底层开始删除空目录
            for root, dirs, files in os.walk(root_dir, topdown=False):
                # 跳过根目录
                if root == root_dir:
                    continue

                try:
                    # 如果目录为空，删除它
                    if not os.listdir(root):
                        os.rmdir(root)
                        if device_id:
                            self.thread_safe_log('debug', f"删除空目录: {root}", device_id)
                        else:
                            self.logger.debug(f"删除空目录: {root}")
                except OSError as e:
                    # 目录不为空或其他错误，跳过
                    if device_id:
                        self.thread_safe_log('debug', f"无法删除目录: {root}, 错误: {e}", device_id)
                    else:
                        self.logger.debug(f"无法删除目录: {root}, 错误: {e}")

        except Exception as e:
            if device_id:
                self.thread_safe_log('error', f"清理空目录异常: {e}", device_id)
            else:
                self.logger.error(f"清理空目录异常: {e}")

    def upload_to_cloud_storage(self, local_dir, device_serial, device_id=None, delete_after_upload=False, device_path=None):
        """通用云存储上传方法"""
        if not self.cloud_upload_enabled:
            if device_id:
                self.thread_safe_log('warning', "云存储上传功能未启用", device_id)
            else:
                self.logger.warning("云存储上传功能未启用")
            return False

        try:
            if self.cloud_provider == 'qiniu':
                return self.upload_to_qiniu(local_dir, device_serial, device_id, delete_after_upload, device_path)
            elif self.cloud_provider == 'aliyun_oss':
                return self.upload_to_aliyun_oss(local_dir, device_serial, device_id, delete_after_upload, device_path)
            else:
                if device_id:
                    self.thread_safe_log('error', f"不支持的云存储提供商: {self.cloud_provider}", device_id)
                else:
                    self.logger.error(f"不支持的云存储提供商: {self.cloud_provider}")
                return False

        except Exception as e:
            if device_id:
                self.thread_safe_log('error', f"云存储上传异常: {e}", device_id)
            else:
                self.logger.error(f"云存储上传异常: {e}")
            return False

    def upload_to_aliyun_oss(self, local_dir, device_serial, device_id=None, delete_after_upload=False, device_path=None):
        """上传文件到阿里云OSS"""
        if not self.cloud_uploader or not self.cloud_uploader.enabled:
            if device_id:
                self.thread_safe_log('warning', "阿里云OSS上传器未启用", device_id)
            else:
                self.logger.warning("阿里云OSS上传器未启用")
            return False

        try:
            upload_success = self.cloud_uploader.upload_directory(local_dir, device_serial, device_id, delete_after_upload)

            # 上报文件上传完成事件（阿里云OSS）
            if self.report_service and upload_success and device_path:
                try:
                    # 获取上传统计信息
                    stats = self.cloud_uploader.upload_stats
                    upload_stats = {
                        'total_files': stats.get('total_files', 0),
                        'uploaded_files': stats.get('uploaded_files', 0),
                        'failed_files': stats.get('failed_files', 0),
                        'total_size': stats.get('total_size', 0),
                        'uploaded_size': stats.get('uploaded_size', 0),
                        'cloud_provider': 'aliyun_oss'
                    }

                    additional_info = {
                        'local_backup_dir': local_dir,
                        'upload_duration': None  # 可以在后续版本中添加计时功能
                    }

                    self.report_service.report_upload_completed(
                        device_serial=device_serial,
                        device_path=device_path,
                        upload_stats=upload_stats,
                        additional_info=additional_info
                    )

                    if device_id:
                        self.thread_safe_log('info', "文件上传完成事件上报成功（阿里云OSS）", device_id)
                    else:
                        self.logger.info("文件上传完成事件上报成功（阿里云OSS）")

                except Exception as e:
                    if device_id:
                        self.thread_safe_log('warning', f"文件上传完成事件上报失败（阿里云OSS）: {e}", device_id)
                    else:
                        self.logger.warning(f"文件上传完成事件上报失败（阿里云OSS）: {e}")

            return upload_success

        except Exception as e:
            if device_id:
                self.thread_safe_log('error', f"阿里云OSS上传异常: {e}", device_id)
            else:
                self.logger.error(f"阿里云OSS上传异常: {e}")
            return False

    def start_cloud_upload(self, local_dir, device_serial, device_id=None, delete_after_upload=False, wait_for_completion=False, device_path=None):
        """启动云存储上传任务（可选择同步或异步）"""
        if not self.cloud_upload_enabled:
            return False

        try:
            provider_name = "七牛云" if self.cloud_provider == 'qiniu' else "阿里云OSS"
            if device_id:
                self.thread_safe_log('info', f"提交{provider_name}上传任务: {local_dir}", device_id)
            else:
                self.logger.info(f"提交{provider_name}上传任务: {local_dir}")

            if wait_for_completion:
                # 同步执行上传任务
                if device_id:
                    self.thread_safe_log('info', "等待上传完成...", device_id)
                else:
                    self.logger.info("等待上传完成...")

                return self.upload_to_cloud_storage(local_dir, device_serial, device_id, delete_after_upload, device_path)
            else:
                # 异步执行上传任务
                future = self.upload_executor.submit(self.upload_to_cloud_storage, local_dir, device_serial, device_id, delete_after_upload, device_path)
                return True

        except Exception as e:
            provider_name = "七牛云" if self.cloud_provider == 'qiniu' else "阿里云OSS"
            if device_id:
                self.thread_safe_log('error', f"启动{provider_name}上传任务失败: {e}", device_id)
            else:
                self.logger.error(f"启动{provider_name}上传任务失败: {e}")
            return False

    def start_qiniu_upload(self, local_dir, device_serial, device_id=None, delete_after_upload=False, wait_for_completion=False, device_path=None):
        """启动七牛云上传任务（可选择同步或异步）"""
        if not self.qiniu_enabled:
            return False

        try:
            if device_id:
                self.thread_safe_log('info', f"提交七牛云上传任务: {local_dir}", device_id)
            else:
                self.logger.info(f"提交七牛云上传任务: {local_dir}")

            if wait_for_completion:
                # 同步执行上传任务
                if device_id:
                    self.thread_safe_log('info', "等待上传完成...", device_id)
                else:
                    self.logger.info("等待上传完成...")

                return self.upload_to_qiniu(local_dir, device_serial, device_id, delete_after_upload, device_path)
            else:
                # 异步执行上传任务
                future = self.upload_executor.submit(self.upload_to_qiniu, local_dir, device_serial, device_id, delete_after_upload, device_path)
                return True

        except Exception as e:
            if device_id:
                self.thread_safe_log('error', f"启动七牛云上传任务失败: {e}", device_id)
            else:
                self.logger.error(f"启动七牛云上传任务失败: {e}")
            return False
            
    def clear_dji_device(self, mount_point):
        """清空DJI设备中的所有数据"""
        try:
            self.logger.info(f"开始清空DJI设备数据: {mount_point}")
            
            if not os.path.exists(mount_point):
                self.logger.error(f"挂载点不存在: {mount_point}")
                return False
                
            if not os.path.ismount(mount_point):
                self.logger.warning(f"路径不是挂载点: {mount_point}")
                
            # 执行卸载命令
            unmount_cmd = f"umount '{mount_point}'"
            self.logger.info(f"执行卸载命令: {unmount_cmd}")
            
            try:
                result = subprocess.run(unmount_cmd, shell=True, capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    self.logger.info(f"设备卸载成功: {mount_point}")
                    
                    # 清理挂载点目录
                    if mount_point.startswith('/media/dji_device_'):
                        try:
                            os.rmdir(mount_point)
                            self.logger.info(f"清理挂载点目录: {mount_point}")
                        except OSError:
                            self.logger.warning(f"清理挂载点目录失败: {mount_point}")
                    
                    return True
                else:
                    self.logger.error(f"设备卸载失败: {result.stderr}")
                    return False
                    
            except subprocess.TimeoutExpired:
                self.logger.error("设备卸载超时")
                return False
            except Exception as e:
                self.logger.error(f"设备卸载异常: {e}")
                return False
                
        except Exception as e:
            self.logger.error(f"卸载DJI设备异常: {e}")
            return False
            
    def clear_dji_device(self, mount_point, device_id=None):
        """清空DJI设备中的所有数据（支持多设备并发）"""
        try:
            if device_id:
                self.thread_safe_log('info', f"开始清空DJI设备数据: {mount_point}", device_id)
            else:
                self.logger.info(f"开始清空DJI设备数据: {mount_point}")
            
            # 统计清理前的文件数量
            if device_id:
                self.thread_safe_log('info', "统计清理前的文件数量...", device_id)
            else:
                self.logger.info("统计清理前的文件数量...")
                
            file_count_before = 0
            try:
                for root, dirs, files in os.walk(mount_point):
                    file_count_before += len(files)
                self.logger.info(f"清理前文件数量: {file_count_before}")
            except Exception as e:
                self.logger.warning(f"统计清理前文件数量失败: {e}")
                file_count_before = -1
            
            # 删除所有文件和文件夹
            self.logger.info("执行清理命令...")
            clear_commands = [
                f"find '{mount_point}' -mindepth 1 -delete 2>/dev/null || true",
                f"rm -rf '{mount_point}'/* 2>/dev/null || true",
                f"rm -rf '{mount_point}'/.[!.]* 2>/dev/null || true"  # 删除隐藏文件
            ]
            
            for i, cmd in enumerate(clear_commands, 1):
                self.logger.info(f"执行清理命令 {i}/{len(clear_commands)}: {cmd}")
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
                    self.logger.info(f"清理命令 {i} 完成，返回码: {result.returncode}")
                    if result.stderr:
                        self.logger.debug(f"清理命令 {i} stderr: {result.stderr}")
                except subprocess.TimeoutExpired:
                    self.logger.warning(f"清理命令 {i} 超时")
                except Exception as e:
                    self.logger.warning(f"清理命令 {i} 异常: {e}")
            
            # 统计清理后的文件数量
            self.logger.info("统计清理后的文件数量...")
            file_count_after = 0
            try:
                for root, dirs, files in os.walk(mount_point):
                    file_count_after += len(files)
                self.logger.info(f"清理后文件数量: {file_count_after}")
            except Exception as e:
                self.logger.warning(f"统计清理后文件数量失败: {e}")
                file_count_after = -1
            
            # 验证清理结果
            if file_count_before >= 0 and file_count_after >= 0:
                if file_count_after == 0:
                    self.logger.info("DJI设备数据清空成功")
                    return True
                elif file_count_after < file_count_before:
                    self.logger.warning(f"部分文件清理成功，剩余 {file_count_after} 个文件")
                    return True  # 部分成功也算成功
                else:
                    self.logger.error("文件清理可能失败，文件数量未减少")
                    return False
            else:
                # 如果统计失败，检查目录是否为空
                try:
                    import os
                    if not os.listdir(mount_point):
                        self.logger.info("目录为空，清理成功")
                        return True
                    else:
                        self.logger.warning("目录不为空，但统计失败")
                        return True  # 保守认为成功
                except Exception as e:
                    self.logger.warning(f"检查目录状态失败: {e}")
                    return True  # 保守认为成功
                
        except Exception as e:
            self.logger.error(f"清空DJI设备数据异常: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False
            
    def process_dji_device(self, device):
        """处理单个DJI设备（在独立线程中运行）"""
        device_path = device.device_node
        device_id = os.path.basename(device_path)
        
        try:
            self.thread_safe_log('info', f"开始处理DJI设备: {device_path}", device_id)
            
            # 获取DJI设备序列号
            device_serial = self.get_dji_device_serial(device_path)
            
            # 获取设备信息
            device_info = {
                'device_path': device_path,
                'device_serial': device_serial,
                'timestamp': datetime.now().isoformat(),
                'vendor': device.get('ID_VENDOR', 'Unknown'),
                'model': device.get('ID_MODEL', 'Unknown'),
                'serial': device.get('ID_SERIAL', 'Unknown'),
                'size': device.get('ID_PART_SIZE', 'Unknown'),
                'thread_id': threading.current_thread().name
            }
            
            # 线程安全地更新设备字典
            with self.device_lock:
                self.dji_devices[device_path] = device_info
            
            self.thread_safe_log('info', f"DJI设备信息: {json.dumps(device_info, indent=2, ensure_ascii=False)}", device_id)
            
            # 等待设备稳定
            self.thread_safe_log('info', "等待设备稳定...", device_id)
            time.sleep(2)
            
            # 尝试挂载设备
            self.thread_safe_log('info', "尝试挂载设备...", device_id)
            mount_point = self.mount_dji_device(device_path)
            
            if mount_point:
                self.thread_safe_log('info', f"DJI设备挂载成功: {mount_point}", device_id)

                # 更新设备信息
                with self.device_lock:
                    if device_path in self.dji_devices:
                        self.dji_devices[device_path]['mount_point'] = mount_point
                        self.dji_devices[device_path]['mount_success'] = True

                # 上报设备连接事件
                if self.report_service:
                    try:
                        additional_info = {
                            'device_type': device_info.get('model', 'Unknown'),
                            'vendor': device_info.get('vendor', 'Unknown'),
                            'thread_id': device_info.get('thread_id', 'Unknown')
                        }
                        self.report_service.report_device_connected(
                            device_serial=device_serial,
                            device_path=device_path,
                            mount_point=mount_point,
                            additional_info=additional_info
                        )
                        self.thread_safe_log('info', "设备连接事件上报完成", device_id)
                    except Exception as e:
                        self.thread_safe_log('warning', f"设备连接事件上报失败: {e}", device_id)
                
                # 列出文件结构
                self.list_files_and_folders(mount_point)
                
                # 执行自动备份流程
                self.thread_safe_log('info', "=" * 50, device_id)
                self.thread_safe_log('info', "开始执行DJI设备自动备份流程", device_id)
                self.thread_safe_log('info', "=" * 50, device_id)
                
                # 1. 备份数据到用户目录
                backup_result = self.backup_dji_data(mount_point, device_serial, device_id)

                if backup_result and backup_result != False:
                    self.thread_safe_log('info', "数据备份成功", device_id)

                    # 启动云存储上传任务
                    upload_success = False
                    if self.cloud_upload_enabled:
                        # 使用实际的备份目录路径
                        try:
                            backup_dir = backup_result  # backup_result 现在是实际的备份目录路径

                            if os.path.exists(backup_dir):
                                # 从配置文件读取设置
                                if CLOUD_CONFIG_AVAILABLE:
                                    current_config = get_current_config()
                                    delete_after_upload = current_config.get('delete_after_upload', True)
                                    wait_for_completion = current_config.get('wait_for_upload', True)
                                else:
                                    # 兼容旧配置
                                    delete_after_upload = getattr(sys.modules.get('qiniu_config'), 'QINIU_DELETE_AFTER_UPLOAD', True)
                                    wait_for_completion = getattr(sys.modules.get('qiniu_config'), 'QINIU_WAIT_FOR_UPLOAD', True)

                                provider_name = "七牛云" if self.cloud_provider == 'qiniu' else "阿里云OSS"
                                if wait_for_completion:
                                    self.thread_safe_log('info', f"启动{provider_name}上传任务（同步等待完成）...", device_id)
                                else:
                                    self.thread_safe_log('info', f"启动{provider_name}上传任务（异步）...", device_id)

                                # 根据配置决定是否等待上传完成和删除本地文件
                                upload_success = self.start_cloud_upload(
                                    backup_dir,
                                    device_serial,
                                    device_id,
                                    delete_after_upload=delete_after_upload,
                                    wait_for_completion=wait_for_completion,
                                    device_path=device_path
                                )

                                if upload_success:
                                    if delete_after_upload and wait_for_completion:
                                        self.thread_safe_log('info', f"{provider_name}上传完成，本地文件已删除", device_id)
                                    elif wait_for_completion:
                                        self.thread_safe_log('info', f"{provider_name}上传完成，本地文件已保留", device_id)
                                    else:
                                        self.thread_safe_log('info', f"{provider_name}上传任务已启动（异步）", device_id)
                                else:
                                    self.thread_safe_log('error', f"{provider_name}上传失败，保留本地文件", device_id)
                            else:
                                self.thread_safe_log('warning', f"备份目录不存在，跳过云上传: {backup_dir}", device_id)
                        except Exception as e:
                            self.thread_safe_log('error', f"启动云上传失败: {e}", device_id)
                    else:
                        self.thread_safe_log('info', "云存储上传功能未启用，保留本地备份", device_id)
                        upload_success = True  # 如果不使用云上传，认为成功
                    
                    self.thread_safe_log('info', "开始清空设备...", device_id)
                    
                    # 2. 清空设备数据
                    clear_success = self.clear_dji_device(mount_point, device_id)
                    
                    if clear_success:
                        self.thread_safe_log('info', "设备数据清空成功，开始卸载设备...", device_id)
                        
                        # 3. 卸载设备
                        unmount_success = self.unmount_dji_device(device_path, device_id)
                        
                        if unmount_success:
                            self.thread_safe_log('info', "=" * 50, device_id)
                            self.thread_safe_log('info', "DJI设备自动备份流程完成!", device_id)
                            self.thread_safe_log('info', f"备份位置: ~/{device_serial}", device_id)
                            self.thread_safe_log('info', "设备已清空并卸载", device_id)
                            self.thread_safe_log('info', "=" * 50, device_id)
                        else:
                            self.thread_safe_log('error', "设备卸载失败", device_id)
                    else:
                        self.thread_safe_log('error', "设备数据清空失败，跳过卸载", device_id)
                else:
                    self.thread_safe_log('error', "数据备份失败，跳过清空和卸载", device_id)
                
            else:
                self.thread_safe_log('error', f"DJI设备挂载失败: {device_path}", device_id)
                with self.device_lock:
                    if device_path in self.dji_devices:
                        self.dji_devices[device_path]['mount_success'] = False
                
        except Exception as e:
            self.thread_safe_log('error', f"处理DJI设备异常: {e}", device_id)
            import traceback
            self.thread_safe_log('error', f"异常详情: {traceback.format_exc()}", device_id)
            
    def handle_device_add(self, device):
        """处理设备插入事件（提交到线程池）"""
        try:
            device_path = device.device_node
            device_id = os.path.basename(device_path)
            
            self.thread_safe_log('info', f"检测到DJI设备插入: {device_path}", device_id)
            
            # 提交到线程池处理
            future = self.executor.submit(self.process_dji_device, device)
            self.thread_safe_log('info', f"DJI设备处理任务已提交到线程池", device_id)
            
            # 可选：添加回调处理任务完成
            def task_done_callback(future):
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                    self.thread_safe_log('info', f"DJI设备处理任务完成", device_id)
                except Exception as e:
                    self.thread_safe_log('error', f"DJI设备处理任务异常: {e}", device_id)
                    
            future.add_done_callback(task_done_callback)
            
        except Exception as e:
            self.logger.error(f"提交DJI设备处理任务异常: {e}")
            
    def handle_device_remove(self, device):
        """处理设备拔出事件"""
        try:
            device_path = device.device_node
            device_id = os.path.basename(device_path)

            self.thread_safe_log('info', f"检测到DJI设备拔出: {device_path}", device_id)

            # 获取设备信息
            device_info = None
            device_serial = None
            mount_point = None

            with self.device_lock:
                if device_path in self.dji_devices:
                    device_info = self.dji_devices[device_path]
                    device_serial = device_info.get('device_serial', 'Unknown')
                    mount_point = device_info.get('mount_point')
                    self.thread_safe_log('info', f"拔出设备信息: {json.dumps(device_info, indent=2, ensure_ascii=False)}", device_id)
                    del self.dji_devices[device_path]

            # 上报设备断开事件
            if self.report_service and device_serial:
                try:
                    additional_info = {
                        'device_type': device_info.get('model', 'Unknown') if device_info else 'Unknown',
                        'vendor': device_info.get('vendor', 'Unknown') if device_info else 'Unknown',
                        'thread_id': device_info.get('thread_id', 'Unknown') if device_info else 'Unknown'
                    }
                    self.report_service.report_device_disconnected(
                        device_serial=device_serial,
                        device_path=device_path,
                        mount_point=mount_point,
                        additional_info=additional_info
                    )
                    self.thread_safe_log('info', "设备断开事件上报完成", device_id)
                except Exception as e:
                    self.thread_safe_log('warning', f"设备断开事件上报失败: {e}", device_id)

            # 卸载设备
            self.unmount_dji_device(device_path, device_id)

        except Exception as e:
            self.logger.error(f"处理DJI设备拔出异常: {e}")
            
    def handle_device_event(self, device):
        """处理设备事件"""
        try:
            if not self.is_dji_device(device):
                return
                
            action = device.action
            device_path = device.device_node
            
            if action == 'add':
                self.handle_device_add(device)
            elif action == 'remove':
                self.handle_device_remove(device)
            else:
                self.logger.debug(f"忽略DJI设备事件: {action} - {device_path}")
                
        except Exception as e:
            self.logger.error(f"处理设备事件异常: {e}")
            
    def scan_existing_dji_devices(self):
        """扫描现有的DJI设备"""
        try:
            self.logger.info("扫描现有DJI设备...")
            
            # 扫描所有块设备
            for device in self.context.list_devices(subsystem='block'):
                if self.is_dji_device(device):
                    self.logger.info(f"发现现有DJI设备: {device.device_node}")
                    self.handle_device_add(device)
                    
        except Exception as e:
            self.logger.error(f"扫描现有DJI设备异常: {e}")
            
    def run(self):
        """运行监控服务"""
        try:
            self.logger.info("DJI设备监控服务开始运行...")
            
            # 扫描现有设备
            self.scan_existing_dji_devices()
            
            # 开始监控
            self.logger.info("开始监控DJI设备插拔事件...")
            
            for device in iter(self.monitor.poll, None):
                if device is not None:
                    self.handle_device_event(device)
                    
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，停止监控...")
        except Exception as e:
            self.logger.error(f"监控服务异常: {e}")
        finally:
            self.logger.info("DJI设备监控服务已停止")
            
def main():
    """主函数"""
    try:
        # 检查权限
        if os.geteuid() != 0:
            print("警告: 建议以root权限运行此服务以获得完整功能")
            
        # 创建并运行监控器
        monitor = DJIDeviceMonitor()
        monitor.run()
        
    except Exception as e:
        print(f"启动DJI监控服务失败: {e}")
        sys.exit(1)
        
if __name__ == "__main__":
    main()

