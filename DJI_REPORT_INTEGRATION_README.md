# DJI设备监控服务上报接口集成说明

## 概述

本文档说明了DJI设备监控服务与上报接口的集成实现。上报接口会在以下三个关键事件发生时记录相关信息：

1. **设备连接事件** - DJI设备插入并成功挂载时
2. **设备断开事件** - DJI设备被拔出时  
3. **文件上传完成事件** - 设备中所有文件上传到云存储完成时

## 功能特性

### 上报信息内容

每个事件都会记录以下核心信息：
- **设备序列号(SN)** - DJI飞机的唯一标识
- **USB接口号** - 设备连接的USB端口信息
- **设备路径** - 系统分配的设备路径（如/dev/sdb1）
- **时间戳** - 事件发生的精确时间
- **额外信息** - 设备型号、厂商等补充信息

### 上传统计信息（仅上传完成事件）
- 总文件数量和成功上传数量
- 总文件大小和已上传大小
- 云存储提供商（七牛云/阿里云OSS）
- 失败文件数量

## 文件结构

```
dji_monitor_service/
├── dji_monitor.py              # 主监控服务（已集成上报功能）
├── dji_report_service.py       # 上报服务实现
├── test_integration.py         # 集成测试脚本
└── DJI_REPORT_INTEGRATION_README.md  # 本说明文档
```

## 使用方法

### 1. 直接运行监控服务

```bash
# 以root权限运行（推荐）
sudo python3 dji_monitor.py

# 或普通用户运行（会使用临时日志文件）
python3 dji_monitor.py
```

### 2. 查看上报日志

```bash
# 查看系统日志（root权限）
tail -f /var/log/dji_report.log

# 查看临时日志（普通用户）
tail -f /tmp/dji_report.log
```

### 3. 运行集成测试

```bash
python3 test_integration.py
```

## 上报日志示例

### 设备连接事件
```
2025-06-29 11:00:55,077 - INFO - [DJI_REPORT] ============================================================
2025-06-29 11:00:55,077 - INFO - [DJI_REPORT] DJI设备连接事件上报
2025-06-29 11:00:55,077 - INFO - [DJI_REPORT] ============================================================
2025-06-29 11:00:55,077 - INFO - [DJI_REPORT] 设备序列号(SN): 1581F5FED4F1D8000A00
2025-06-29 11:00:55,077 - INFO - [DJI_REPORT] 设备路径: /dev/sdb1
2025-06-29 11:00:55,077 - INFO - [DJI_REPORT] USB接口号: Bus 002 Device 003: ID 2ca3:001f
2025-06-29 11:00:55,078 - INFO - [DJI_REPORT] 挂载点: /media/dji_device_sdb1_20250629_110055
2025-06-29 11:00:55,078 - INFO - [DJI_REPORT] 上报时间: 2025-06-29T11:00:55.077686
2025-06-29 11:00:55,078 - INFO - [DJI_REPORT] 设备连接事件上报完成
2025-06-29 11:00:55,078 - INFO - [DJI_REPORT] ============================================================
```

### 设备断开事件
```
2025-06-29 11:05:32,081 - INFO - [DJI_REPORT] ============================================================
2025-06-29 11:05:32,081 - INFO - [DJI_REPORT] DJI设备断开事件上报
2025-06-29 11:05:32,082 - INFO - [DJI_REPORT] ============================================================
2025-06-29 11:05:32,082 - INFO - [DJI_REPORT] 设备序列号(SN): 1581F5FED4F1D8000A00
2025-06-29 11:05:32,082 - INFO - [DJI_REPORT] 设备路径: /dev/sdb1
2025-06-29 11:05:32,082 - INFO - [DJI_REPORT] USB接口号: Bus 002 Device 003: ID 2ca3:001f
2025-06-29 11:05:32,082 - INFO - [DJI_REPORT] 上报时间: 2025-06-29T11:05:32.081864
2025-06-29 11:05:32,082 - INFO - [DJI_REPORT] 设备断开事件上报完成
2025-06-29 11:05:32,082 - INFO - [DJI_REPORT] ============================================================
```

### 文件上传完成事件
```
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT] ============================================================
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT] DJI设备文件上传完成事件上报
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT] ============================================================
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT] 设备序列号(SN): 1581F5FED4F1D8000A00
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT] 设备路径: /dev/sdb1
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT] USB接口号: Bus 002 Device 003: ID 2ca3:001f
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT] 上报时间: 2025-06-29T11:03:15.087314
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT] 上传统计信息:
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT]   total_files: 45
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT]   uploaded_files: 43
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT]   failed_files: 2
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT]   total_size: 2.3 GB
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT]   uploaded_size: 2.2 GB
2025-06-29 11:03:15,087 - INFO - [DJI_REPORT]   cloud_provider: aliyun_oss
2025-06-29 11:03:15,088 - INFO - [DJI_REPORT] 文件上传完成事件上报完成
2025-06-29 11:03:15,088 - INFO - [DJI_REPORT] ============================================================
```

## 技术实现

### 集成点

1. **设备连接上报** - 在`process_dji_device()`方法中设备挂载成功后调用
2. **设备断开上报** - 在`handle_device_remove()`方法中设备信息获取后调用  
3. **上传完成上报** - 在`upload_to_qiniu()`和`upload_to_aliyun_oss()`方法中上传成功后调用

### 容错机制

- **权限处理** - 无法写入系统日志时自动切换到临时日志文件
- **异常处理** - 上报失败不影响主要的设备监控功能
- **线程安全** - 支持多设备并发处理时的安全上报

### 配置选项

上报服务支持以下配置：
- 日志文件路径（默认：/var/log/dji_report.log）
- 日志级别（默认：INFO）
- USB端口信息获取超时时间

## 注意事项

1. **权限要求** - 建议以root权限运行以获得完整的USB设备信息和系统日志写入权限
2. **日志轮转** - 生产环境建议配置日志轮转以防止日志文件过大
3. **监控集成** - 可以通过监控日志文件来实现实时的设备状态监控和告警

## 扩展说明

当前实现将上报信息输出到日志文件中。如需要发送到远程服务器或数据库，可以在`dji_report_service.py`中的相应方法中添加HTTP请求或数据库写入逻辑。

所有上报方法都返回布尔值表示成功/失败，便于后续扩展和错误处理。
