#!/bin/bash

# DJI设备监控服务安装脚本

set -e

echo "=== DJI设备监控服务安装程序（多云存储版） ==="
echo ""

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请以root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

echo "1. 安装系统依赖..."

# 更新包列表
apt-get update

# 安装必要的系统工具
apt-get install -y udev usbutils lsof python3 python3-pip 

echo "2. 安装Python依赖..."

# 安装Python依赖包
pip3 install pyudev qiniu oss2 -i https://pypi.tuna.tsinghua.edu.cn/simple/

echo "3. 创建服务目录..."

# 创建服务目录
mkdir -p /opt/dji_monitor

echo "4. 复制服务文件..."

# 复制服务脚本
cp dji_monitor.py /opt/dji_monitor/
cp cloud_storage_config.py /opt/dji_monitor/
cp aliyun_oss_uploader.py /opt/dji_monitor/
# 保持向后兼容，如果存在旧配置文件也复制
if [ -f "qiniu_config.py" ]; then
    cp qiniu_config.py /opt/dji_monitor/
fi
chmod +x /opt/dji_monitor/dji_monitor.py
chmod +x /opt/dji_monitor/cloud_storage_config.py
chmod +x /opt/dji_monitor/aliyun_oss_uploader.py
if [ -f "/opt/dji_monitor/qiniu_config.py" ]; then
    chmod +x /opt/dji_monitor/qiniu_config.py
fi

# 复制systemd服务文件
cp dji-monitor.service /etc/systemd/system/

echo "5. 配置systemd服务..."

# 重新加载systemd配置
systemctl daemon-reload

# 启用服务（开机自启动）
systemctl enable dji-monitor.service

echo "6. 启动服务..."

# 启动服务
systemctl start dji-monitor.service

echo "7. 检查服务状态..."

# 等待服务启动
sleep 2

# 检查服务状态
if systemctl is-active --quiet dji-monitor.service; then
    echo "✅ DJI设备监控服务安装成功并正在运行!"
    echo ""
    echo "服务管理命令:"
    echo "  查看状态: sudo systemctl status dji-monitor"
    echo "  查看日志: sudo journalctl -u dji-monitor -f"
    echo "  查看专用日志: sudo tail -f /var/log/dji_monitor.log"
    echo "  重启服务: sudo systemctl restart dji-monitor"
    echo "  停止服务: sudo systemctl stop dji-monitor"
    echo ""
    echo "现在可以连接DJI设备测试自动挂载功能!"
else
    echo "❌ 服务启动失败，请检查日志:"
    echo "  sudo journalctl -u dji-monitor -n 20"
    exit 1
fi

echo ""
echo "=== 安装完成 ==="

