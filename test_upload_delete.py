#!/usr/bin/env python3
"""
测试上传后删除本地文件功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from dji_monitor import DJIDeviceMonitor
    from qiniu_config import *
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖都已安装")
    sys.exit(1)

def create_test_files(test_dir, num_files=5):
    """创建测试文件"""
    print(f"创建测试文件到: {test_dir}")
    
    # 创建一些测试文件
    for i in range(num_files):
        file_path = os.path.join(test_dir, f"test_file_{i}.txt")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"这是测试文件 {i}\n")
            f.write(f"文件内容: {'测试数据' * 100}\n")  # 增加一些内容
        print(f"创建文件: {file_path}")
    
    # 创建子目录和文件
    sub_dir = os.path.join(test_dir, "subdir")
    os.makedirs(sub_dir, exist_ok=True)
    
    for i in range(2):
        file_path = os.path.join(sub_dir, f"sub_file_{i}.txt")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"这是子目录测试文件 {i}\n")
            f.write(f"子目录内容: {'子目录测试数据' * 50}\n")
        print(f"创建子目录文件: {file_path}")

def count_files(directory):
    """统计目录中的文件数量"""
    count = 0
    for root, dirs, files in os.walk(directory):
        count += len(files)
    return count

def test_upload_and_delete():
    """测试上传和删除功能"""
    print("=" * 60)
    print("测试上传后删除本地文件功能")
    print("=" * 60)
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = os.path.join(temp_dir, "test_backup")
        os.makedirs(test_dir, exist_ok=True)
        
        # 创建测试文件
        create_test_files(test_dir)
        
        # 统计创建的文件数量
        file_count_before = count_files(test_dir)
        print(f"\n创建的文件数量: {file_count_before}")
        
        # 创建DJI监控器实例
        monitor = DJIDeviceMonitor()
        
        # 测试上传功能
        print(f"\n开始测试上传功能...")
        print(f"七牛云上传启用状态: {monitor.qiniu_enabled}")
        
        if not monitor.qiniu_enabled:
            print("警告: 七牛云上传功能未启用，无法测试完整功能")
            return
        
        # 测试配置
        device_serial = "TEST_DEVICE_001"
        
        print(f"配置信息:")
        print(f"  QINIU_DELETE_AFTER_UPLOAD: {QINIU_DELETE_AFTER_UPLOAD}")
        print(f"  QINIU_WAIT_FOR_UPLOAD: {QINIU_WAIT_FOR_UPLOAD}")
        
        # 执行上传测试
        print(f"\n开始上传测试...")
        upload_success = monitor.start_qiniu_upload(
            test_dir,
            device_serial,
            device_id="TEST",
            delete_after_upload=QINIU_DELETE_AFTER_UPLOAD,
            wait_for_completion=QINIU_WAIT_FOR_UPLOAD
        )
        
        print(f"上传结果: {'成功' if upload_success else '失败'}")
        
        # 统计剩余文件数量
        file_count_after = count_files(test_dir)
        print(f"剩余文件数量: {file_count_after}")
        
        # 分析结果
        if QINIU_DELETE_AFTER_UPLOAD and QINIU_WAIT_FOR_UPLOAD:
            if file_count_after == 0:
                print("✅ 测试成功: 文件已上传并删除")
            elif file_count_after < file_count_before:
                print("⚠️  部分成功: 部分文件已删除")
            else:
                print("❌ 测试失败: 文件未被删除")
        else:
            if file_count_after == file_count_before:
                print("✅ 测试成功: 文件已保留（符合配置）")
            else:
                print("⚠️  意外结果: 文件数量发生变化")
        
        # 显示剩余文件
        if file_count_after > 0:
            print(f"\n剩余文件列表:")
            for root, dirs, files in os.walk(test_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, test_dir)
                    print(f"  {rel_path}")

def main():
    """主函数"""
    try:
        test_upload_and_delete()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
