# DJI监控服务 - 上传后删除本地文件功能

## 功能概述

本次更新为DJI监控服务添加了**上传成功后自动删除本地文件**的功能，帮助节省本地存储空间。

## 新增功能

### 1. 上传后删除本地文件
- 文件上传到七牛云成功后，自动删除本地备份文件
- 只删除成功上传的文件，失败的文件会保留
- 自动清理空目录

### 2. 可配置的上传策略
- 支持同步和异步上传模式
- 可配置是否在上传后删除本地文件
- 灵活的配置选项

## 配置选项

在 `qiniu_config.py` 文件中新增了以下配置项：

```python
# 本地文件管理配置
QINIU_DELETE_AFTER_UPLOAD = True  # 上传成功后是否删除本地文件
QINIU_WAIT_FOR_UPLOAD = True      # 是否等待上传完成再继续后续操作
```

### 配置说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `QINIU_DELETE_AFTER_UPLOAD` | `True` | 设置为 `True` 时，上传成功后删除本地文件；设置为 `False` 时保留本地文件 |
| `QINIU_WAIT_FOR_UPLOAD` | `True` | 设置为 `True` 时，等待上传完成后再继续；设置为 `False` 时异步上传 |

### 配置组合效果

| QINIU_DELETE_AFTER_UPLOAD | QINIU_WAIT_FOR_UPLOAD | 效果 |
|---------------------------|----------------------|------|
| `True` | `True` | 同步上传，成功后删除本地文件 |
| `True` | `False` | 异步上传，后台删除成功上传的文件 |
| `False` | `True` | 同步上传，保留本地文件 |
| `False` | `False` | 异步上传，保留本地文件 |

## 工作流程

1. **设备检测**: 检测到DJI设备插入
2. **数据备份**: 将设备数据备份到本地用户目录
3. **云端上传**: 根据配置上传文件到七牛云
4. **文件管理**: 根据配置决定是否删除本地文件
5. **设备清理**: 清空设备数据并卸载

## 安全特性

### 文件删除安全机制
- **只删除成功上传的文件**: 只有确认上传成功的文件才会被删除
- **保留失败文件**: 上传失败的文件会保留在本地
- **详细日志记录**: 记录每个文件的上传和删除状态
- **空目录清理**: 自动清理因文件删除而产生的空目录

### 错误处理
- 上传失败时保留所有本地文件
- 网络异常时自动重试
- 详细的错误日志记录

## 使用示例

### 启用上传后删除功能
```python
# 在 qiniu_config.py 中设置
QINIU_DELETE_AFTER_UPLOAD = True
QINIU_WAIT_FOR_UPLOAD = True
```

### 保留本地备份
```python
# 在 qiniu_config.py 中设置
QINIU_DELETE_AFTER_UPLOAD = False
QINIU_WAIT_FOR_UPLOAD = True
```

### 异步上传模式
```python
# 在 qiniu_config.py 中设置
QINIU_DELETE_AFTER_UPLOAD = True
QINIU_WAIT_FOR_UPLOAD = False
```

## 测试功能

提供了测试脚本 `test_upload_delete.py` 来验证功能：

```bash
python3 test_upload_delete.py
```

测试脚本会：
1. 创建临时测试文件
2. 模拟上传过程
3. 验证文件删除功能
4. 显示测试结果

## 日志信息

新功能会在日志中记录详细信息：

```
[线程名][设备ID] 开始上传到七牛云: /path/to/backup
[线程名][设备ID] 准备上传 10 个文件，总大小: 2.5 MB
[线程名][设备ID] 上传文件 1/10: device_serial/20231201/file1.jpg
[线程名][设备ID] 上传成功: device_serial/20231201/file1.jpg
[线程名][设备ID] 七牛云上传完成: 成功 10/10 个文件
[线程名][设备ID] 开始删除已成功上传的本地文件，共 10 个
[线程名][设备ID] 本地文件删除完成: 成功删除 10/10 个文件
[线程名][设备ID] 释放空间: 2.5 MB
```

## 注意事项

1. **确保网络稳定**: 上传过程中网络中断可能导致部分文件删除失败
2. **存储空间**: 启用删除功能前确保云端存储空间充足
3. **备份策略**: 建议在重要数据上传前进行额外备份
4. **配置测试**: 首次使用时建议先用测试数据验证配置

## 故障排除

### 文件未被删除
- 检查 `QINIU_DELETE_AFTER_UPLOAD` 配置
- 查看日志中的上传状态
- 确认网络连接正常

### 上传失败
- 检查七牛云配置信息
- 验证网络连接
- 查看详细错误日志

### 部分文件删除
- 正常现象，只有成功上传的文件会被删除
- 检查失败文件的错误信息
- 可手动重新上传失败的文件

## 更新日志

### v2.0.0 (当前版本)
- ✅ 新增上传后删除本地文件功能
- ✅ 新增可配置的上传策略
- ✅ 新增空目录自动清理
- ✅ 新增详细的文件操作日志
- ✅ 新增测试脚本
- ✅ 改进错误处理机制
