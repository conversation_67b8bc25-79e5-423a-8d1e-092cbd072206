#!/usr/bin/env python3
"""
测试DJI监控服务与上报服务的集成
"""

import sys
import os

def test_imports():
    """测试导入是否正常"""
    print("测试导入...")
    
    try:
        # 测试上报服务导入
        from dji_report_service import get_report_service, DJIReportService
        print("✓ 上报服务导入成功")

        # 测试获取上报服务实例（使用临时日志文件）
        try:
            report_service = get_report_service()
            print("✓ 上报服务实例创建成功")
        except PermissionError:
            # 权限不足时使用临时日志文件
            report_service = DJIReportService(log_file="/tmp/dji_report_test.log")
            print("✓ 上报服务实例创建成功（临时模式）")
        
        # 测试监控服务导入
        from dji_monitor import DJIDeviceMonitor
        print("✓ 监控服务导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_report_service():
    """测试上报服务功能"""
    print("\n测试上报服务功能...")

    try:
        from dji_report_service import DJIReportService
        # 使用临时日志文件
        report_service = DJIReportService(log_file="/tmp/dji_report_test.log")
        
        # 测试设备连接上报
        print("测试设备连接上报...")
        report_service.report_device_connected(
            device_serial="TEST_DEVICE_001",
            device_path="/dev/sdb1",
            mount_point="/media/dji_device_test",
            additional_info={"test": True}
        )
        print("✓ 设备连接上报测试成功")
        
        # 测试设备断开上报
        print("测试设备断开上报...")
        report_service.report_device_disconnected(
            device_serial="TEST_DEVICE_001",
            device_path="/dev/sdb1",
            mount_point="/media/dji_device_test",
            additional_info={"test": True}
        )
        print("✓ 设备断开上报测试成功")
        
        # 测试上传完成上报
        print("测试上传完成上报...")
        upload_stats = {
            'total_files': 10,
            'uploaded_files': 8,
            'failed_files': 2,
            'total_size': 1024*1024*100,  # 100MB
            'uploaded_size': 1024*1024*80,  # 80MB
            'cloud_provider': 'qiniu'
        }
        report_service.report_upload_completed(
            device_serial="TEST_DEVICE_001",
            device_path="/dev/sdb1",
            upload_stats=upload_stats,
            additional_info={"test": True}
        )
        print("✓ 上传完成上报测试成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 上报服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monitor_service_init():
    """测试监控服务初始化"""
    print("\n测试监控服务初始化...")

    try:
        from dji_monitor import DJIDeviceMonitor

        # 创建监控服务实例（不启动监控），使用临时日志文件
        monitor = DJIDeviceMonitor(log_file="/tmp/dji_monitor_test.log")
        print("✓ 监控服务实例创建成功")
        
        # 检查上报服务是否正确初始化
        if hasattr(monitor, 'report_service') and monitor.report_service is not None:
            print("✓ 上报服务已正确集成到监控服务中")
        else:
            print("⚠ 上报服务未正确集成到监控服务中")
        
        return True
        
    except Exception as e:
        print(f"✗ 监控服务初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("DJI监控服务与上报服务集成测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_imports,
        test_report_service,
        test_monitor_service_init
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！集成成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
