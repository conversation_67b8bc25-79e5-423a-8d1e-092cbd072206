#!/usr/bin/env python3
"""
测试备份路径修复的脚本
验证备份目录路径是否正确传递给上传功能
"""

import os
import sys
import tempfile
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_files(test_dir):
    """创建测试文件"""
    print(f"创建测试文件到: {test_dir}")
    
    # 创建子目录结构
    subdirs = ['DCIM', 'MISC']
    for subdir in subdirs:
        subdir_path = os.path.join(test_dir, subdir)
        os.makedirs(subdir_path, exist_ok=True)
        
        # 在每个子目录中创建测试文件
        for i in range(2):
            file_path = os.path.join(subdir_path, f"test_file_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(f"测试文件内容 - {subdir} - {i}\n")
                f.write(f"创建时间: {datetime.now()}\n")
    
    print(f"测试文件创建完成")

def test_backup_path_generation():
    """测试备份路径生成逻辑"""
    print("=" * 60)
    print("测试备份路径生成逻辑")
    print("=" * 60)
    
    try:
        from dji_monitor import DJIDeviceMonitor
        
        # 创建监控器实例（使用临时日志文件）
        import tempfile
        temp_log = tempfile.mktemp(suffix='.log')
        monitor = DJIDeviceMonitor(log_file=temp_log)
        
        # 测试备份目录生成
        device_serial = f"TEST_DEVICE_{int(time.time())}"
        backup_home = "/tmp/test_backup_home"
        
        # 确保测试目录存在
        os.makedirs(backup_home, exist_ok=True)
        
        print(f"设备序列号: {device_serial}")
        print(f"备份主目录: {backup_home}")
        
        # 测试第一次生成（应该是简单的路径）
        backup_dir1 = monitor.generate_unique_backup_dir(device_serial, backup_home)
        print(f"第一次生成的备份目录: {backup_dir1}")
        
        # 创建第一个目录
        os.makedirs(backup_dir1, exist_ok=True)
        
        # 测试第二次生成（应该包含时间戳和随机ID）
        backup_dir2 = monitor.generate_unique_backup_dir(device_serial, backup_home)
        print(f"第二次生成的备份目录: {backup_dir2}")
        
        # 验证两个路径不同
        if backup_dir1 != backup_dir2:
            print("✅ 备份目录生成逻辑正常，能够生成唯一路径")
        else:
            print("❌ 备份目录生成逻辑异常，生成了相同的路径")
        
        # 清理测试目录
        import shutil
        if os.path.exists(backup_home):
            shutil.rmtree(backup_home)
            
    except Exception as e:
        print(f"❌ 测试备份路径生成失败: {e}")
        import traceback
        traceback.print_exc()

def test_upload_path_logic():
    """测试上传路径逻辑"""
    print("\n" + "=" * 60)
    print("测试上传路径逻辑")
    print("=" * 60)
    
    try:
        from dji_monitor import DJIDeviceMonitor
        
        # 创建监控器实例（使用临时日志文件）
        import tempfile
        temp_log = tempfile.mktemp(suffix='.log')
        monitor = DJIDeviceMonitor(log_file=temp_log)
        
        # 创建临时测试目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 模拟备份目录
            device_serial = f"TEST_DEVICE_{int(time.time())}"
            backup_dir = os.path.join(temp_dir, f"{device_serial}_20241227_123456_abcd1234")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 创建测试文件
            create_test_files(backup_dir)
            
            print(f"模拟备份目录: {backup_dir}")
            
            # 检查目录中的文件
            file_count = 0
            for root, dirs, files in os.walk(backup_dir):
                file_count += len(files)
                for file in files:
                    print(f"  文件: {os.path.join(root, file)}")
            
            print(f"总文件数: {file_count}")
            
            if file_count > 0:
                print("✅ 测试文件创建成功，可以进行上传测试")
                
                # 测试上传功能（不实际上传，只测试文件发现逻辑）
                if monitor.cloud_upload_enabled:
                    print(f"云存储提供商: {monitor.cloud_provider}")
                    print("开始测试上传文件发现逻辑...")
                    
                    # 模拟上传过程中的文件发现
                    upload_files = []
                    for root, dirs, files in os.walk(backup_dir):
                        for file in files:
                            local_file_path = os.path.join(root, file)
                            upload_files.append(local_file_path)
                    
                    if upload_files:
                        print(f"✅ 发现 {len(upload_files)} 个文件可以上传")
                        for file_path in upload_files:
                            print(f"  - {file_path}")
                    else:
                        print("❌ 没有发现可上传的文件")
                else:
                    print("⚠️  云存储上传功能未启用")
            else:
                print("❌ 测试文件创建失败")
                
    except Exception as e:
        print(f"❌ 测试上传路径逻辑失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("备份路径修复测试开始")
    print(f"测试时间: {datetime.now()}")
    
    # 测试备份路径生成
    test_backup_path_generation()
    
    # 测试上传路径逻辑
    test_upload_path_logic()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    print("\n修复说明:")
    print("1. backup_dji_data() 方法现在返回实际的备份目录路径")
    print("2. 上传过程使用实际的备份目录路径，而不是硬编码的路径")
    print("3. 这样可以确保即使备份目录包含时间戳和随机ID，上传也能找到正确的文件")

if __name__ == "__main__":
    main()
