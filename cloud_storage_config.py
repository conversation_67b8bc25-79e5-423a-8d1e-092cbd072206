# -*- coding: utf-8 -*-
"""
云存储配置文件
支持多个云存储提供商：七牛云、阿里云OSS
"""

# ==================== 云存储提供商选择 ====================
# 可选值: 'qiniu', 'aliyun_oss'
CLOUD_STORAGE_PROVIDER = 'aliyun_oss'  # 默认使用七牛云

# ==================== 七牛云配置 ====================
QINIU_CONFIG = {
    # 基本配置
    'access_key': '4I_GtwMsXHsNaW9V4QtvLprssoZE7Z4ZGx3kXsJP',
    'secret_key': 'F8_p1x1g6sTdVyvj01m7e3IKbc7JqA9fNIM9_3dg',
    'bucket_name': 'wurenjidd',
    
    # 上传配置
    'upload_enabled': True,
    'upload_timeout': 36000,  # 上传超时时间（秒）
    'token_expire': 3600,     # Token过期时间（秒）
    
    # 上传策略配置
    'upload_policy': {
        # 可以在这里添加自定义的上传策略
        # 例如：回调URL、文件大小限制等
    },
    
    # 文件命名策略
    'file_prefix': 'dji_backup',  # 文件前缀
    'use_timestamp': True,        # 是否在文件名中包含时间戳
    'preserve_path': True,        # 是否保持原始目录结构
    
    # 上传重试配置
    'max_retries': 3,            # 最大重试次数
    'retry_delay': 5,            # 重试间隔（秒）
    
    # 本地文件管理配置
    'delete_after_upload': True,  # 上传成功后是否删除本地文件
    'wait_for_upload': True,      # 是否等待上传完成再继续后续操作
    
    # 并发上传配置
    'max_upload_workers': 5,     # 最大并发上传线程数
    'chunk_size': 4 * 1024 * 1024,  # 分片上传大小（4MB）
    
    # 日志配置
    'log_level': 'INFO',         # 日志级别
    'log_uploads': True,         # 是否记录上传详情
    
    # 存储区域配置（可选）
    # 华东: 'z0', 华北: 'z1', 华南: 'z2', 北美: 'na0', 东南亚: 'as0'
    'zone': None  # 自动选择最优区域
}

# ==================== 阿里云OSS配置 ====================
ALIYUN_OSS_CONFIG = {
    # 基本配置
    'access_key_id': 'LTAI5tFvyeiya1rmrAqFa7Si',
    'access_key_secret': '******************************',
    'region': 'cn-zhangjiakou',
    'bucket_name': 'shared-drone-equipment',
    'role_arn': 'acs:ram::1323939537738415:role/drone',  # 可选，用于STS临时凭证
    
    # 上传配置
    'upload_enabled': True,
    'upload_timeout': 36000,  # 上传超时时间（秒）
    'token_expire': 3600,     # STS Token过期时间（秒）
    
    # 文件命名策略
    'file_prefix': 'dji_backup',  # 文件前缀
    'use_timestamp': True,        # 是否在文件名中包含时间戳
    'preserve_path': True,        # 是否保持原始目录结构
    
    # 上传重试配置
    'max_retries': 3,            # 最大重试次数
    'retry_delay': 5,            # 重试间隔（秒）
    
    # 本地文件管理配置
    'delete_after_upload': True,  # 上传成功后是否删除本地文件
    'wait_for_upload': True,      # 是否等待上传完成再继续后续操作
    
    # 并发上传配置
    'max_upload_workers': 5,     # 最大并发上传线程数
    'chunk_size': 4 * 1024 * 1024,  # 分片上传大小（4MB）
    
    # 日志配置
    'log_level': 'INFO',         # 日志级别
    'log_uploads': True,         # 是否记录上传详情
    
    # OSS特有配置
    'endpoint': None,            # 自动根据region生成，也可手动指定
    'use_ssl': True,             # 是否使用HTTPS
    'connect_timeout': 60,       # 连接超时时间（秒）
    'multipart_threshold': 100 * 1024 * 1024,  # 分片上传阈值（100MB）
    'max_concurrency': 10,       # 分片上传最大并发数
}

# ==================== 通用配置 ====================
GENERAL_CONFIG = {
    # 全局开关
    'cloud_upload_enabled': True,  # 是否启用云存储上传功能
    
    # 备份策略
    'backup_strategy': 'upload_and_keep',  # 'upload_and_delete', 'upload_and_keep', 'local_only'
    
    # 错误处理
    'continue_on_upload_error': True,  # 上传失败时是否继续其他操作
    'max_upload_failures': 5,         # 最大允许的上传失败次数
    
    # 监控和统计
    'enable_upload_stats': True,      # 是否启用上传统计
    'stats_report_interval': 300,     # 统计报告间隔（秒）
}

# ==================== 兼容性配置 ====================
# 为了保持向后兼容，保留原有的配置变量名
def get_current_config():
    """获取当前选择的云存储配置"""
    if CLOUD_STORAGE_PROVIDER == 'qiniu':
        return QINIU_CONFIG
    elif CLOUD_STORAGE_PROVIDER == 'aliyun_oss':
        return ALIYUN_OSS_CONFIG
    else:
        raise ValueError(f"不支持的云存储提供商: {CLOUD_STORAGE_PROVIDER}")

# 向后兼容的配置变量
current_config = get_current_config()

# 七牛云兼容性变量
QINIU_ACCESS_KEY = QINIU_CONFIG['access_key']
QINIU_SECRET_KEY = QINIU_CONFIG['secret_key']
QINIU_BUCKET_NAME = QINIU_CONFIG['bucket_name']
QINIU_UPLOAD_ENABLED = QINIU_CONFIG['upload_enabled'] if CLOUD_STORAGE_PROVIDER == 'qiniu' else False
QINIU_UPLOAD_TIMEOUT = QINIU_CONFIG['upload_timeout']
QINIU_TOKEN_EXPIRE = QINIU_CONFIG['token_expire']
QINIU_UPLOAD_POLICY = QINIU_CONFIG['upload_policy']
QINIU_FILE_PREFIX = QINIU_CONFIG['file_prefix']
QINIU_USE_TIMESTAMP = QINIU_CONFIG['use_timestamp']
QINIU_PRESERVE_PATH = QINIU_CONFIG['preserve_path']
QINIU_MAX_RETRIES = QINIU_CONFIG['max_retries']
QINIU_RETRY_DELAY = QINIU_CONFIG['retry_delay']
QINIU_DELETE_AFTER_UPLOAD = QINIU_CONFIG['delete_after_upload']
QINIU_WAIT_FOR_UPLOAD = QINIU_CONFIG['wait_for_upload']
QINIU_MAX_UPLOAD_WORKERS = QINIU_CONFIG['max_upload_workers']
QINIU_CHUNK_SIZE = QINIU_CONFIG['chunk_size']
QINIU_LOG_LEVEL = QINIU_CONFIG['log_level']
QINIU_LOG_UPLOADS = QINIU_CONFIG['log_uploads']
QINIU_ZONE = QINIU_CONFIG['zone']

# 通用配置变量
CLOUD_UPLOAD_ENABLED = GENERAL_CONFIG['cloud_upload_enabled'] and current_config['upload_enabled']
DELETE_AFTER_UPLOAD = current_config['delete_after_upload']
WAIT_FOR_UPLOAD = current_config['wait_for_upload']
MAX_RETRIES = current_config['max_retries']
RETRY_DELAY = current_config['retry_delay']
MAX_UPLOAD_WORKERS = current_config['max_upload_workers']

# ==================== 配置验证函数 ====================
def validate_config():
    """验证当前配置的有效性"""
    if CLOUD_STORAGE_PROVIDER not in ['qiniu', 'aliyun_oss']:
        raise ValueError(f"不支持的云存储提供商: {CLOUD_STORAGE_PROVIDER}")
    
    config = get_current_config()
    
    # 验证必要的配置项
    if CLOUD_STORAGE_PROVIDER == 'qiniu':
        required_fields = ['access_key', 'secret_key', 'bucket_name']
    elif CLOUD_STORAGE_PROVIDER == 'aliyun_oss':
        required_fields = ['access_key_id', 'access_key_secret', 'region', 'bucket_name']
    
    for field in required_fields:
        if not config.get(field):
            raise ValueError(f"缺少必要的配置项: {field}")
    
    return True

# ==================== 配置切换函数 ====================
def switch_cloud_provider(provider):
    """切换云存储提供商"""
    global CLOUD_STORAGE_PROVIDER, current_config
    global CLOUD_UPLOAD_ENABLED, DELETE_AFTER_UPLOAD, WAIT_FOR_UPLOAD
    global MAX_RETRIES, RETRY_DELAY, MAX_UPLOAD_WORKERS
    
    if provider not in ['qiniu', 'aliyun_oss']:
        raise ValueError(f"不支持的云存储提供商: {provider}")
    
    CLOUD_STORAGE_PROVIDER = provider
    current_config = get_current_config()
    
    # 更新通用配置变量
    CLOUD_UPLOAD_ENABLED = GENERAL_CONFIG['cloud_upload_enabled'] and current_config['upload_enabled']
    DELETE_AFTER_UPLOAD = current_config['delete_after_upload']
    WAIT_FOR_UPLOAD = current_config['wait_for_upload']
    MAX_RETRIES = current_config['max_retries']
    RETRY_DELAY = current_config['retry_delay']
    MAX_UPLOAD_WORKERS = current_config['max_upload_workers']
    
    return True

# 初始化时验证配置
if __name__ == "__main__":
    try:
        validate_config()
        print(f"云存储配置验证成功，当前提供商: {CLOUD_STORAGE_PROVIDER}")
    except Exception as e:
        print(f"云存储配置验证失败: {e}")
