# DJI设备专用监控服务（自动备份增强版）

这是一个专门为DJI设备设计的监控服务，能够自动检测DJI设备的连接和断开，执行自动挂载操作，详细列出设备中的所有文件和文件夹结构，并**自动备份数据到用户目录，然后清空设备并卸载**。

## 🆕 **v2.0 自动备份增强版新功能**

### 🎯 **完整的自动备份流程**
1. **设备检测** → 自动识别DJI设备连接
2. **序列号提取** → 从lsusb信息中提取设备序列号（如：DJI-1581F87L524CU00SZ0K7）
3. **自动挂载** → 智能挂载设备到系统
4. **文件列表** → 详细显示所有文件和文件夹
5. **数据备份** → 复制所有数据到用户根目录下的序列号文件夹
6. **设备清空** → 删除设备中的所有数据
7. **自动卸载** → 安全卸载设备

### 🔍 **智能序列号提取**
- **精准识别**: 从lsusb输出中提取DJI设备序列号
- **多重策略**: DJI-前缀识别 + 正则表达式匹配 + 设备属性获取
- **备用方案**: 无法获取时使用时间戳生成唯一标识

### 📁 **智能备份管理**
- **用户目录**: 自动识别实际用户主目录（支持sudo环境）
- **唯一命名**: 以设备序列号命名备份文件夹
- **冲突处理**: 如果文件夹已存在，自动添加时间戳
- **权限设置**: 自动设置正确的文件所有者和权限

### 🧹 **安全数据清理**
- **多重清理**: 使用多种命令确保数据完全清除
- **隐藏文件**: 包括隐藏文件和系统文件的清理
- **安全验证**: 清理前后文件数量对比验证

## 功能特性

### 🎯 **DJI设备专用检测**
- **精准识别**: 专门针对DJI设备的USB厂商ID (2ca3) 进行检测
- **关键词匹配**: 支持DJI、Mavic、Phantom、Inspire、Osmo等设备关键词
- **多重验证**: 通过USB信息、设备属性、父设备等多重机制确保准确识别

### 🚀 **智能自动挂载**
- **多策略挂载**: 6种不同的挂载策略，确保最大兼容性
- **文件系统支持**: 支持VFAT、exFAT、NTFS等多种文件系统
- **权限设置**: 自动设置合适的用户权限 (uid=1000,gid=1000)
- **挂载点管理**: 自动创建唯一命名的挂载点，避免冲突

### 📁 **完整文件列表**
- **递归遍历**: 自动递归列出所有文件和文件夹（最大深度3层）
- **详细信息**: 显示文件大小、修改时间、文件类型
- **树形结构**: 以清晰的树形结构显示目录层次
- **文件系统信息**: 显示总容量、已使用空间、可用空间

### 💾 **自动备份功能** ⭐ **新增**
- **序列号提取**: 从lsusb信息中自动提取DJI设备序列号
- **智能备份**: 复制所有数据到用户根目录下的序列号文件夹
- **统计信息**: 显示备份的文件数量和总大小
- **权限管理**: 自动设置正确的文件所有者

### 🧹 **自动清空功能** ⭐ **新增**
- **安全清理**: 多重命令确保数据完全清除
- **验证机制**: 清理前后文件数量对比
- **隐藏文件**: 包括隐藏文件的完整清理

### 🔄 **自动卸载清理**
- **智能卸载**: 备份和清空完成后自动卸载设备
- **进程检查**: 检测占用进程并提供强制卸载选项
- **记录管理**: 自动维护挂载记录，确保完整清理

### 📊 **详细日志记录**
- **完整流程**: 记录从检测到卸载的完整过程
- **多重输出**: 同时输出到文件和控制台
- **结构化信息**: JSON格式记录设备详细信息
- **错误诊断**: 详细的错误信息和故障排除建议

## 工作流程详解

### DJI设备插入时的完整流程：

```
1. 🔍 设备检测
   ├── 监听udev块设备事件
   ├── 识别DJI设备（USB厂商ID: 2ca3）
   └── 提取设备序列号（如：DJI-1581F87L524CU00SZ0K7）

2. 🔧 自动挂载
   ├── 等待设备稳定（3秒）
   ├── 尝试6种挂载策略
   └── 创建挂载点：/media/dji_device_时间戳

3. 📁 文件系统分析
   ├── 等待文件系统准备（2秒）
   ├── 显示文件系统信息（容量、使用情况）
   └── 递归列出所有文件和文件夹

4. 💾 自动备份流程
   ├── 确定用户主目录（支持sudo环境）
   ├── 创建备份目录：~/DJI-1581F87L524CU00SZ0K7
   ├── 复制所有数据到备份目录
   ├── 统计备份文件数量和大小
   └── 设置正确的文件权限

5. 🧹 设备清空
   ├── 统计清理前文件数量
   ├── 执行多重清理命令
   ├── 删除所有文件（包括隐藏文件）
   └── 验证清理结果

6. 🔄 自动卸载
   ├── 安全卸载设备
   ├── 清理挂载点目录
   └── 清理挂载记录
```

### 日志输出示例：

```
==================================================
开始执行DJI设备自动备份流程
==================================================
2025-06-07 16:30:15,123 - INFO - 提取到DJI设备序列号: DJI-1581F87L524CU00SZ0K7
2025-06-07 16:30:18,125 - INFO - DJI设备挂载成功: /dev/sdb -> /media/dji_device_1701234567
2025-06-07 16:30:20,130 - INFO - 文件系统信息:
2025-06-07 16:30:20,131 - INFO -   总容量: 22.1 GB
2025-06-07 16:30:20,132 - INFO -   已使用: 15.3 GB
2025-06-07 16:30:20,133 - INFO -   可用空间: 6.8 GB
2025-06-07 16:30:20,134 - INFO - 文件和文件夹结构:
2025-06-07 16:30:20,135 - INFO - ├── 📁 DCIM/
2025-06-07 16:30:20,136 - INFO - │   ├── 📁 100MEDIA/
2025-06-07 16:30:20,137 - INFO - │   │   ├── 📄 DJI_0001.JPG (5.2 MB) [2025-06-07 10:30:15]
2025-06-07 16:30:20,138 - INFO - │   │   └── 📄 DJI_0002.MP4 (125.6 MB) [2025-06-07 10:40:18]
2025-06-07 16:30:25,140 - INFO - 开始备份DJI设备数据: /media/dji_device_1701234567 -> ~/DJI-1581F87L524CU00SZ0K7
2025-06-07 16:30:25,141 - INFO - 备份用户: ubuntu, 主目录: /home/<USER>
2025-06-07 16:30:25,142 - INFO - 创建备份目录: /home/<USER>/DJI-1581F87L524CU00SZ0K7
2025-06-07 16:30:30,145 - INFO - 数据复制完成:
2025-06-07 16:30:30,146 - INFO -   备份目录: /home/<USER>/DJI-1581F87L524CU00SZ0K7
2025-06-07 16:30:30,147 - INFO -   文件数量: 25
2025-06-07 16:30:30,148 - INFO -   总大小: 2.3 GB
2025-06-07 16:30:30,149 - INFO - 数据备份成功，开始清空设备...
2025-06-07 16:30:30,150 - INFO - 开始清空DJI设备数据: /media/dji_device_1701234567
2025-06-07 16:30:30,151 - INFO - 清理前文件数量: 25
2025-06-07 16:30:35,155 - INFO - 清理后文件数量: 0
2025-06-07 16:30:35,156 - INFO - DJI设备数据清空成功
2025-06-07 16:30:35,157 - INFO - 设备数据清空成功，开始卸载设备...
2025-06-07 16:30:35,158 - INFO - 卸载成功: /media/dji_device_1701234567
==================================================
DJI设备自动备份流程完成!
备份位置: ~/DJI-1581F87L524CU00SZ0K7
设备已清空并卸载
==================================================
```

## 系统要求

- Ubuntu 18.04 或更高版本
- Python 3.6 或更高版本
- root权限（用于设备挂载操作）
- 必要的系统工具：udev、usbutils、lsof

## 安装方法

### 自动安装（推荐）

1. 下载服务文件包并解压
2. 进入服务目录
3. 以root权限运行安装脚本：

```bash
cd dji_monitor_service
sudo ./install.sh
```

安装脚本会自动：
- 安装系统依赖包 (udev, usbutils, lsof)
- 安装Python依赖包 (pyudev)
- 创建服务目录和文件
- 配置systemd服务
- 启用自启动并启动服务

### 手动安装

1. 安装系统依赖：
```bash
sudo apt-get update
sudo apt-get install -y udev usbutils lsof python3 python3-pip
```

2. 安装Python依赖：
```bash
sudo pip3 install pyudev
```

3. 创建服务目录：
```bash
sudo mkdir -p /opt/dji_monitor
```

4. 复制服务文件：
```bash
sudo cp dji_monitor.py /opt/dji_monitor/
sudo chmod +x /opt/dji_monitor/dji_monitor.py
```

5. 安装systemd服务：
```bash
sudo cp dji-monitor.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable dji-monitor.service
sudo systemctl start dji-monitor.service
```

## 使用方法

### 基本使用流程

1. **启动服务**（安装后自动启动）
2. **连接DJI设备**（USB连接）
3. **自动执行**：
   - 设备检测和挂载
   - 文件列表显示
   - 数据自动备份到 `~/DJI-设备序列号/`
   - 设备数据清空
   - 设备自动卸载
4. **查看备份**：在用户主目录下找到以设备序列号命名的文件夹

### 服务管理命令

```bash
# 查看服务状态
sudo systemctl status dji-monitor

# 启动服务
sudo systemctl start dji-monitor

# 停止服务
sudo systemctl stop dji-monitor

# 重启服务
sudo systemctl restart dji-monitor

# 启用开机自启动
sudo systemctl enable dji-monitor

# 禁用开机自启动
sudo systemctl disable dji-monitor
```

### 日志查看

```bash
# 查看系统日志（实时）
sudo journalctl -u dji-monitor -f

# 查看专用日志（实时）
sudo tail -f /var/log/dji_monitor.log

# 查看历史日志
sudo cat /var/log/dji_monitor.log
```

### 备份文件管理

```bash
# 查看所有DJI设备备份
ls -la ~/DJI-*

# 查看特定设备备份内容
ls -la ~/DJI-1581F87L524CU00SZ0K7/

# 查看备份文件大小
du -sh ~/DJI-*
```

## 配置说明

### 自动备份配置

- **备份位置**: 用户主目录（自动检测）
- **文件夹命名**: DJI设备序列号（如：DJI-1581F87L524CU00SZ0K7）
- **冲突处理**: 如果文件夹已存在，添加时间戳后缀
- **权限设置**: 自动设置为实际用户所有

### 序列号提取配置

- **主要方式**: 从lsusb输出中提取DJI-前缀的序列号
- **备用方式**: 正则表达式匹配长字符串
- **最后方案**: 使用时间戳生成唯一标识

### 清空策略配置

1. `find /mount_point -mindepth 1 -delete` - 递归删除所有内容
2. `rm -rf /mount_point/*` - 删除所有可见文件
3. `rm -rf /mount_point/.[!.]*` - 删除隐藏文件

## 故障排除

### 备份失败

1. 检查用户权限：
```bash
ls -la /home/
whoami
echo $SUDO_USER
```

2. 检查磁盘空间：
```bash
df -h /home/
```

3. 手动测试备份：
```bash
sudo mkdir -p /home/<USER>/test_backup
sudo cp -r /media/dji_device_*/* /home/<USER>/test_backup/
```

### 清空失败

1. 检查挂载状态：
```bash
mount | grep dji
lsof /media/dji_device_*
```

2. 手动清空测试：
```bash
sudo find /media/dji_device_* -mindepth 1 -delete
```

3. 检查文件权限：
```bash
ls -la /media/dji_device_*/
```

### 序列号提取失败

1. 检查lsusb输出：
```bash
lsusb | grep -i dji
```

2. 检查设备属性：
```bash
sudo udevadm info --query=all --name=/dev/sdb | grep SERIAL
```

## 安全注意事项

⚠️ **重要警告**：
- 此服务会**自动清空DJI设备中的所有数据**
- 请确保备份功能正常工作后再使用
- 建议先在测试环境中验证功能
- 重要数据请额外手动备份

✅ **安全特性**：
- 只处理DJI设备，不影响其他USB设备
- 备份成功后才执行清空操作
- 完整的错误处理和日志记录
- 多重验证确保操作安全

## 卸载方法

### 自动卸载

```bash
sudo ./uninstall.sh
```

### 手动卸载

```bash
# 停止并禁用服务
sudo systemctl stop dji-monitor
sudo systemctl disable dji-monitor

# 删除服务文件
sudo rm /etc/systemd/system/dji-monitor.service
sudo systemctl daemon-reload

# 删除程序文件
sudo rm -rf /opt/dji_monitor

# 删除日志文件
sudo rm -f /var/log/dji_monitor.log

# 清理临时文件
sudo rm -f /tmp/dji_mounts.log
sudo find /media -name "dji_device_*" -type d -empty -delete
```

## 更新日志

### v2.0 自动备份增强版
- ✅ 新增自动序列号提取功能
- ✅ 新增自动备份到用户目录功能
- ✅ 新增自动清空设备数据功能
- ✅ 新增完整的自动化流程
- ✅ 增强用户权限管理
- ✅ 优化日志输出格式

### v1.0 基础监控版
- ✅ DJI设备自动检测
- ✅ 智能自动挂载
- ✅ 完整文件列表显示
- ✅ 自动卸载清理

## 许可证

本项目采用MIT许可证。

## 支持

如有问题或建议，请：

1. 查看日志文件排查问题
2. 检查系统兼容性和依赖
3. 确认DJI设备连接状态
4. 验证权限配置正确
5. 测试备份和清空功能

---

**注意**: 此服务专门为DJI设备设计，具有**自动清空设备数据**的功能，请谨慎使用并确保重要数据已备份。

## 功能特性

### 🎯 **DJI设备专用检测**
- **精准识别**: 专门针对DJI设备的USB厂商ID (2ca3) 进行检测
- **关键词匹配**: 支持DJI、Mavic、Phantom、Inspire、Osmo等设备关键词
- **多重验证**: 通过USB信息、设备属性、父设备等多重机制确保准确识别

### 🚀 **智能自动挂载**
- **多策略挂载**: 6种不同的挂载策略，确保最大兼容性
- **文件系统支持**: 支持VFAT、exFAT、NTFS等多种文件系统
- **权限设置**: 自动设置合适的用户权限 (uid=1000,gid=1000)
- **挂载点管理**: 自动创建唯一命名的挂载点，避免冲突

### 📁 **完整文件列表**
- **递归遍历**: 自动递归列出所有文件和文件夹（最大深度3层）
- **详细信息**: 显示文件大小、修改时间、文件类型
- **树形结构**: 以清晰的树形结构显示目录层次
- **文件系统信息**: 显示总容量、已使用空间、可用空间

### 🔄 **自动卸载清理**
- **智能卸载**: 设备拔出时自动卸载并清理挂载点
- **进程检查**: 检测占用进程并提供强制卸载选项
- **记录管理**: 自动维护挂载记录，确保完整清理

### 📊 **详细日志记录**
- **实时日志**: 记录所有操作过程和结果
- **多重输出**: 同时输出到文件和控制台
- **结构化信息**: JSON格式记录设备详细信息
- **错误诊断**: 详细的错误信息和故障排除建议

## 系统要求

- Ubuntu 18.04 或更高版本
- Python 3.6 或更高版本
- root权限（用于设备挂载操作）
- 必要的系统工具：udev、usbutils、lsof

## 安装方法

### 自动安装（推荐）

1. 下载服务文件包并解压
2. 进入服务目录
3. 以root权限运行安装脚本：

```bash
cd dji_monitor_service
sudo ./install.sh
```

安装脚本会自动：
- 安装系统依赖包 (udev, usbutils, lsof)
- 安装Python依赖包 (pyudev)
- 创建服务目录和文件
- 配置systemd服务
- 启用自启动并启动服务

### 手动安装

1. 安装系统依赖：
```bash
sudo apt-get update
sudo apt-get install -y udev usbutils lsof python3 python3-pip
```

2. 安装Python依赖：
```bash
sudo pip3 install pyudev
```

3. 创建服务目录：
```bash
sudo mkdir -p /opt/dji_monitor
```

4. 复制服务文件：
```bash
sudo cp dji_monitor.py /opt/dji_monitor/
sudo chmod +x /opt/dji_monitor/dji_monitor.py
```

5. 安装systemd服务：
```bash
sudo cp dji-monitor.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable dji-monitor.service
sudo systemctl start dji-monitor.service
```

## 使用方法

### 服务管理命令

```bash
# 查看服务状态
sudo systemctl status dji-monitor

# 启动服务
sudo systemctl start dji-monitor

# 停止服务
sudo systemctl stop dji-monitor

# 重启服务
sudo systemctl restart dji-monitor

# 启用开机自启动
sudo systemctl enable dji-monitor

# 禁用开机自启动
sudo systemctl disable dji-monitor
```

### 日志查看

```bash
# 查看系统日志（实时）
sudo journalctl -u dji-monitor -f

# 查看专用日志（实时）
sudo tail -f /var/log/dji_monitor.log

# 查看历史日志
sudo cat /var/log/dji_monitor.log
```

## 工作流程

### DJI设备插入时

1. **设备检测**: 监听udev块设备事件
2. **DJI识别**: 通过USB厂商ID和关键词识别DJI设备
3. **设备稳定**: 等待设备稳定（3秒）
4. **自动挂载**: 尝试多种挂载策略直到成功
5. **文件系统准备**: 等待文件系统准备就绪（2秒）
6. **文件列表**: 递归列出所有文件和文件夹
7. **记录信息**: 记录设备信息和挂载状态

### DJI设备拔出时

1. **设备检测**: 检测到设备拔出事件
2. **查找挂载点**: 从系统和记录中查找相关挂载点
3. **进程检查**: 检查是否有进程占用挂载点
4. **自动卸载**: 执行卸载操作（正常→强制→延迟）
5. **清理挂载点**: 删除空的挂载点目录
6. **清理记录**: 清理相关的挂载记录

## 日志格式说明

### DJI设备插入示例

```
2025-06-07 16:30:15,123 - INFO - 检测到DJI设备插入: /dev/sdb
2025-06-07 16:30:15,124 - INFO - DJI设备信息: {
  "device_path": "/dev/sdb",
  "timestamp": "2025-06-07T16:30:15.124000",
  "vendor": "DJI Technology Co., Ltd.",
  "model": "DJI-1581F87L524CU00SZ0K7",
  "serial": "DJI_1581F87L524CU00SZ0K7",
  "size": "22.1G"
}
2025-06-07 16:30:18,125 - INFO - 开始挂载DJI设备: /dev/sdb
2025-06-07 16:30:18,126 - INFO - 创建挂载点: /media/dji_device_1701234567
2025-06-07 16:30:18,127 - INFO - 尝试挂载策略: mount /dev/sdb /media/dji_device_1701234567
2025-06-07 16:30:18,128 - INFO - DJI设备挂载成功: /dev/sdb -> /media/dji_device_1701234567
2025-06-07 16:30:20,129 - INFO - 开始列出文件结构: /media/dji_device_1701234567
2025-06-07 16:30:20,130 - INFO - 文件系统信息:
2025-06-07 16:30:20,131 - INFO -   总容量: 22.1 GB
2025-06-07 16:30:20,132 - INFO -   已使用: 15.3 GB
2025-06-07 16:30:20,133 - INFO -   可用空间: 6.8 GB
2025-06-07 16:30:20,134 - INFO - 文件和文件夹结构:
2025-06-07 16:30:20,135 - INFO - ├── 📁 DCIM/
2025-06-07 16:30:20,136 - INFO - │   ├── 📁 100MEDIA/
2025-06-07 16:30:20,137 - INFO - │   │   ├── 📄 DJI_0001.JPG (5.2 MB) [2025-06-07 10:30:15]
2025-06-07 16:30:20,138 - INFO - │   │   ├── 📄 DJI_0002.JPG (4.8 MB) [2025-06-07 10:35:22]
2025-06-07 16:30:20,139 - INFO - │   │   └── 📄 DJI_0003.MP4 (125.6 MB) [2025-06-07 10:40:18]
2025-06-07 16:30:20,140 - INFO - └── 📁 MISC/
2025-06-07 16:30:20,141 - INFO -     └── 📄 readme.txt (1.2 KB) [2025-06-07 09:15:30]
```

### DJI设备拔出示例

```
2025-06-07 16:45:30,456 - INFO - 检测到DJI设备拔出: /dev/sdb
2025-06-07 16:45:30,457 - INFO - 拔出设备信息: {
  "device_path": "/dev/sdb",
  "timestamp": "2025-06-07T16:30:15.124000",
  "vendor": "DJI Technology Co., Ltd.",
  "model": "DJI-1581F87L524CU00SZ0K7",
  "mount_point": "/media/dji_device_1701234567",
  "mount_success": true
}
2025-06-07 16:45:30,458 - INFO - 开始卸载DJI设备: /dev/sdb
2025-06-07 16:45:30,459 - INFO - 卸载挂载点: /media/dji_device_1701234567
2025-06-07 16:45:30,460 - INFO - 执行命令: umount /media/dji_device_1701234567
2025-06-07 16:45:30,461 - INFO - 卸载成功: /media/dji_device_1701234567
```

## 配置说明

### 服务配置

服务配置了以下特性来确保高可靠性：

- `Restart=always`: 服务异常退出时自动重启
- `RestartSec=5`: 重启间隔5秒
- `OOMScoreAdjust=-1000`: 降低被OOM killer杀死的优先级
- `KillMode=process`: 只杀死主进程，不杀死子进程

### DJI设备识别配置

- **USB厂商ID**: `2ca3` (DJI Technology Co., Ltd.)
- **关键词**: `dji`, `DJI`, `mavic`, `phantom`, `inspire`, `osmo`
- **设备类型**: 块设备 (disk/partition)
- **文件深度**: 最大递归深度3层

### 挂载策略配置

1. 直接挂载: `mount /dev/sdX /media/dji_device_XXX`
2. VFAT挂载: `mount -t vfat /dev/sdX /media/dji_device_XXX`
3. exFAT挂载: `mount -t exfat -o uid=1000,gid=1000,umask=0022 /dev/sdX /media/dji_device_XXX`
4. NTFS挂载: `mount -t ntfs-3g -o uid=1000,gid=1000,umask=0022 /dev/sdX /media/dji_device_XXX`
5. 强制挂载: `mount -o force,rw /dev/sdX /media/dji_device_XXX`
6. 只读挂载: `mount -o ro /dev/sdX /media/dji_device_XXX`

## 故障排除

### 服务无法启动

1. 检查Python依赖：
```bash
python3 -c "import pyudev"
```

2. 检查系统工具：
```bash
which udevadm lsusb lsof mount umount
```

3. 检查权限：
```bash
ls -la /opt/dji_monitor/dji_monitor.py
```

4. 查看详细错误：
```bash
sudo journalctl -u dji-monitor -n 50
```

### DJI设备无法检测

1. 检查DJI设备连接：
```bash
lsusb | grep -i dji
lsblk -o NAME,RM,SIZE,TYPE,MOUNTPOINT
```

2. 手动测试检测：
```bash
sudo udevadm monitor --subsystem-match=block
```

3. 检查设备属性：
```bash
sudo udevadm info --query=all --name=/dev/sdb | grep -i dji
```

### 挂载失败

1. 检查文件系统：
```bash
sudo blkid /dev/sdb
sudo file -s /dev/sdb
```

2. 手动测试挂载：
```bash
sudo mkdir -p /media/test_dji
sudo mount /dev/sdb /media/test_dji
```

3. 检查系统日志：
```bash
sudo dmesg | tail -20
```

### 文件列表不完整

1. 检查挂载点权限：
```bash
ls -la /media/dji_device_*
```

2. 手动列出文件：
```bash
sudo find /media/dji_device_* -type f | head -20
```

3. 检查文件系统错误：
```bash
sudo fsck -n /dev/sdb
```

## 卸载方法

### 自动卸载

```bash
sudo ./uninstall.sh
```

### 手动卸载

```bash
# 停止并禁用服务
sudo systemctl stop dji-monitor
sudo systemctl disable dji-monitor

# 删除服务文件
sudo rm /etc/systemd/system/dji-monitor.service
sudo systemctl daemon-reload

# 删除程序文件
sudo rm -rf /opt/dji_monitor

# 删除日志文件
sudo rm -f /var/log/dji_monitor.log

# 清理临时文件
sudo rm -f /tmp/dji_mounts.log
sudo find /media -name "dji_device_*" -type d -empty -delete
```

## 技术实现

### 核心技术

- **pyudev**: 用于监听Linux udev事件
- **systemd**: 用于服务管理和自启动
- **mount/umount**: 用于设备挂载和卸载
- **lsblk/udevadm**: 用于获取设备详细信息
- **lsusb**: 用于USB设备识别
- **lsof**: 用于进程占用检查

### 设备检测流程

1. **事件监听**: 监听udev block设备事件
2. **设备过滤**: 检查是否为块设备（分区或磁盘）
3. **DJI识别**: 通过USB厂商ID和关键词识别
4. **多重验证**: 检查设备属性和父设备信息
5. **事件处理**: 根据事件类型执行相应操作

### 挂载算法

```python
def mount_dji_device(device_path):
    # 1. 检查是否已挂载
    if already_mounted(device_path):
        return existing_mount_point
    
    # 2. 创建唯一挂载点
    mount_point = f"/media/dji_device_{timestamp}"
    create_mount_point(mount_point)
    
    # 3. 尝试多种挂载策略
    for strategy in mount_strategies:
        if try_mount(strategy, device_path, mount_point):
            return mount_point
    
    # 4. 所有策略失败，清理挂载点
    cleanup_mount_point(mount_point)
    return None
```

### 文件列表算法

```python
def list_files_recursive(path, depth, max_depth):
    if depth > max_depth:
        return
    
    # 1. 获取目录内容
    items = get_directory_items(path)
    
    # 2. 排序（文件夹在前）
    items.sort(key=lambda x: (not x.is_dir, x.name))
    
    # 3. 递归处理
    for item in items:
        print_item_info(item)
        if item.is_dir and depth < max_depth:
            list_files_recursive(item.path, depth + 1, max_depth)
```

## 安全考虑

- 只处理DJI设备，不影响其他USB设备
- 使用安全的挂载选项，限制权限
- 自动创建的挂载点使用唯一命名，避免冲突
- 完整的错误处理和日志记录
- 多重检测机制避免误判
- 进程占用检查确保安全卸载

## 许可证

本项目采用MIT许可证。

## 支持

如有问题或建议，请：

1. 查看日志文件排查问题
2. 检查系统兼容性和依赖
3. 确认DJI设备连接状态
4. 验证权限配置正确
5. 测试设备检测功能

---

**注意**: 此服务专门为DJI设备设计，如需监控其他USB设备，请使用通用的USB监控服务。

