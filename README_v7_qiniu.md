# DJI设备监控服务 - 七牛云备份增强版

## 概述

DJI设备监控服务（七牛云备份增强版）是一个专门为DJI设备设计的自动化备份解决方案。当DJI设备连接到系统时，服务会自动执行以下操作：

1. **自动检测和挂载** - 智能识别DJI设备并自动挂载
2. **本地备份** - 将设备中的所有数据复制到用户目录
3. **云端备份** - 自动上传到七牛云存储，实现双重备份
4. **设备清理** - 备份完成后清空设备中的所有数据
5. **自动卸载** - 安全卸载设备

## 核心功能

### 🎯 **智能设备识别**
- **DJI厂商ID检测**: 专门识别USB厂商ID `2ca3` (DJI Technology Co., Ltd.)
- **设备序列号提取**: 从lsusb信息中自动提取设备序列号（如：DJI-1581F87L524CU00SZ0K7）
- **多重验证**: USB信息 + 设备属性 + 父设备检查

### 🔧 **智能自动挂载**
- **6种挂载策略**: 从直接挂载到强制挂载，确保成功率
- **文件系统支持**: VFAT、exFAT、NTFS 等多种格式
- **权限设置**: 自动设置用户权限
- **唯一挂载点**: 避免多设备冲突

### 💾 **本地备份功能**
- **智能用户识别**: 自动检测实际用户（支持sudo环境）
- **唯一目录命名**: 使用设备序列号作为备份目录名
- **完整数据复制**: 复制设备中的所有文件和文件夹
- **权限管理**: 自动设置正确的文件所有者

### ☁️ **七牛云备份功能** ⭐ **新增**
- **异步上传**: 在独立线程中执行，不影响主要备份流程
- **智能重试**: 上传失败时自动重试，最大重试次数可配置
- **进度监控**: 详细的上传进度和统计信息
- **文件组织**: 按设备序列号和日期自动组织云端文件结构

### 🧹 **设备清理功能**
- **完全清空**: 删除设备中的所有文件（包括隐藏文件）
- **多重清理**: 使用多种命令确保数据完全清除
- **验证机制**: 清理前后文件数量对比验证

### 🔄 **多设备并发支持**
- **线程池架构**: 支持最多5个设备同时处理
- **独立处理**: 每个设备在独立线程中处理，互不干扰
- **线程安全**: 所有操作都是线程安全的
- **资源管理**: 智能的资源分配和清理

## 七牛云配置

### 配置文件: `qiniu_config.py`

```python
# 七牛云存储配置
QINIU_ACCESS_KEY = '4I_GtwMsXHsNaW9V4QtvLprssoZE7Z4ZGx3kXsJP'
QINIU_SECRET_KEY = 'F8_p1x1g6sTdVyvj01m7e3IKbc7JqA9fNIM9_3dg'
QINIU_BUCKET_NAME = 'wurenjidd'

# 上传配置
QINIU_UPLOAD_ENABLED = True  # 是否启用七牛云上传
QINIU_UPLOAD_TIMEOUT = 3600  # 上传超时时间（秒）
QINIU_TOKEN_EXPIRE = 3600    # Token过期时间（秒）

# 上传重试配置
QINIU_MAX_RETRIES = 3        # 最大重试次数
QINIU_RETRY_DELAY = 5        # 重试间隔（秒）

# 并发上传配置
QINIU_MAX_UPLOAD_WORKERS = 3 # 最大并发上传线程数
```

### 云端文件组织结构

```
wurenjidd/
├── DJI-1581F87L524CU00SZ0K7/
│   ├── 20250608/
│   │   ├── DCIM/
│   │   │   ├── 100MEDIA/
│   │   │   │   ├── DJI_0001.JPG
│   │   │   │   ├── DJI_0002.JPG
│   │   │   │   └── DJI_0003.MP4
│   │   │   └── ...
│   │   └── MISC/
│   │       └── readme.txt
│   └── 20250609/
│       └── ...
└── DJI-另一个设备序列号/
    └── ...
```

## 工作流程

### DJI设备插入时：
1. **实时检测** → 识别DJI设备连接
2. **设备稳定** → 等待设备稳定
3. **自动挂载** → 使用6种策略尝试挂载
4. **文件列表** → 显示设备中的所有文件和文件夹
5. **本地备份** → 复制所有数据到用户目录（~/设备序列号/）
6. **云端备份** → 异步上传到七牛云存储 ⭐ **新增**
7. **设备清空** → 删除设备中的所有数据
8. **自动卸载** → 安全卸载设备

### DJI设备拔出时：
1. **检测拔出** → 识别设备断开
2. **查找挂载点** → 从系统和记录中查找
3. **安全卸载** → 正常→强制→延迟卸载
4. **清理挂载点** → 删除空目录
5. **清理记录** → 清理相关记录

## 安装和部署

### 系统要求
- Ubuntu 22.04 或更高版本
- Python 3.11+
- root权限（用于设备挂载和系统服务）

### 依赖安装
```bash
# 安装Python依赖
pip3 install pyudev qiniu

# 安装系统工具
sudo apt-get update
sudo apt-get install -y udev usbutils util-linux exfat-fuse ntfs-3g
```

### 服务安装
```bash
# 运行安装脚本
sudo ./install.sh
```

### 服务管理
```bash
# 查看服务状态
sudo systemctl status dji-monitor

# 启动服务
sudo systemctl start dji-monitor

# 停止服务
sudo systemctl stop dji-monitor

# 重启服务
sudo systemctl restart dji-monitor

# 查看实时日志
sudo tail -f /var/log/dji_monitor.log

# 查看七牛云上传统计
sudo journalctl -u dji-monitor -f | grep -i qiniu
```

## 日志示例

### 完整的备份流程日志：
```
==================================================
开始执行DJI设备自动备份流程
==================================================
2025-06-08 14:30:15 - INFO - [DJI-Worker_0][sdb] 提取到DJI设备序列号: DJI-1581F87L524CU00SZ0K7
2025-06-08 14:30:18 - INFO - [DJI-Worker_0][sdb] DJI设备挂载成功: /media/dji_device_92fcbb2e_1749317747252738
2025-06-08 14:30:25 - INFO - [DJI-Worker_0][sdb] 开始备份DJI设备数据: /media/dji_device_92fcbb2e_1749317747252738 -> ~/DJI-1581F87L524CU00SZ0K7
2025-06-08 14:30:30 - INFO - [DJI-Worker_0][sdb] 数据复制完成:
2025-06-08 14:30:30 - INFO - [DJI-Worker_0][sdb]   备份目录: /home/<USER>/DJI-1581F87L524CU00SZ0K7
2025-06-08 14:30:30 - INFO - [DJI-Worker_0][sdb]   文件数量: 25
2025-06-08 14:30:30 - INFO - [DJI-Worker_0][sdb]   总大小: 2.3 GB
2025-06-08 14:30:35 - INFO - [QiNiu-Upload_0][sdb] 开始上传到七牛云: /home/<USER>/DJI-1581F87L524CU00SZ0K7
2025-06-08 14:30:35 - INFO - [QiNiu-Upload_0][sdb] 准备上传 25 个文件，总大小: 2.3 GB
2025-06-08 14:32:15 - INFO - [QiNiu-Upload_0][sdb] 七牛云上传完成: 成功 25/25 个文件
2025-06-08 14:32:15 - INFO - [QiNiu-Upload_0][sdb] 上传大小: 2.3 GB/2.3 GB
2025-06-08 14:32:20 - INFO - [DJI-Worker_0][sdb] 清理前文件数量: 25
2025-06-08 14:32:25 - INFO - [DJI-Worker_0][sdb] 清理后文件数量: 0
2025-06-08 14:32:25 - INFO - [DJI-Worker_0][sdb] DJI设备数据清空成功
==================================================
DJI设备自动备份流程完成!
本地备份: ~/DJI-1581F87L524CU00SZ0K7
云端备份: wurenjidd/DJI-1581F87L524CU00SZ0K7/20250608/
设备已清空并卸载
==================================================
```

## 技术特性

### 🚀 **性能优化**
- **并发处理**: 多设备同时处理，效率提升3倍
- **异步上传**: 云端备份不阻塞主要流程
- **智能重试**: 网络异常时自动重试
- **资源管理**: 智能的线程池和资源分配

### 🛡️ **安全保障**
- **数据验证**: 备份前后文件数量验证
- **权限管理**: 自动设置正确的文件权限
- **异常处理**: 完善的错误处理和回滚机制
- **日志记录**: 详细的操作日志和审计跟踪

### 🔧 **可配置性**
- **七牛云配置**: 灵活的云存储配置
- **重试策略**: 可配置的重试次数和间隔
- **并发控制**: 可调整的线程池大小
- **超时设置**: 可配置的操作超时时间

## 故障排除

### 常见问题

1. **设备无法识别**
   - 检查USB连接
   - 确认设备厂商ID为2ca3
   - 查看系统日志：`dmesg | tail`

2. **挂载失败**
   - 检查文件系统类型
   - 确认挂载点权限
   - 手动测试挂载命令

3. **七牛云上传失败**
   - 检查网络连接
   - 验证AK/SK配置
   - 确认存储空间名称

4. **权限问题**
   - 确保以root权限运行服务
   - 检查用户目录权限
   - 验证sudo配置

### 调试命令
```bash
# 检查USB设备
lsusb | grep -i dji

# 检查挂载状态
mount | grep dji

# 检查服务日志
sudo journalctl -u dji-monitor -f

# 测试七牛云连接
python3 -c "from qiniu import Auth; print('七牛云SDK正常')"
```

## 版本历史

- **v7.0** - 七牛云备份增强版
  - 新增七牛云自动上传功能
  - 异步上传，不影响主要流程
  - 智能重试和进度监控
  - 完善的配置管理

- **v6.0** - 多设备并发支持版
  - 支持多设备同时处理
  - 线程池架构
  - 线程安全机制

- **v5.0** - 自动备份增强版
  - 自动备份到用户目录
  - 设备数据清空功能
  - 完善的错误处理

## 技术支持

如有问题或建议，请查看日志文件：
- 服务日志：`/var/log/dji_monitor.log`
- 系统日志：`sudo journalctl -u dji-monitor`

---

**DJI设备监控服务 - 让DJI设备数据管理变得简单高效！**

