#!/usr/bin/env python3
"""
DJI设备状态上报服务
用于在设备连接、设备拔除、文件上传成功后上报相关信息
"""

import os
import sys
import time
import json
import logging
import threading
from datetime import datetime
from typing import Dict, Any, Optional

class DJIReportService:
    """DJI设备状态上报服务"""
    
    def __init__(self, log_file="/var/log/dji_report.log"):
        """
        初始化上报服务
        
        Args:
            log_file: 日志文件路径
        """
        self.log_file = log_file
        self.setup_logging()
        self.report_lock = threading.Lock()  # 上报操作锁
        
        self.logger.info("DJI设备状态上报服务初始化完成")
        
    def setup_logging(self):
        """设置日志"""
        # 创建日志目录
        log_dir = os.path.dirname(self.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # 创建独立的logger，避免与主服务冲突
        self.logger = logging.getLogger('dji_report_service')
        self.logger.setLevel(logging.INFO)

        # 清除可能存在的处理器
        self.logger.handlers.clear()

        # 创建格式化器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - [DJI_REPORT] %(message)s')

        # 添加文件处理器
        try:
            file_handler = logging.FileHandler(self.log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        except PermissionError:
            # 如果无法写入指定文件，使用临时文件
            temp_log_file = "/tmp/dji_report.log"
            file_handler = logging.FileHandler(temp_log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
            print(f"警告: 无法写入 {self.log_file}，使用临时日志文件: {temp_log_file}")

        # 添加控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # 防止日志向上传播到root logger
        self.logger.propagate = False

    def get_usb_port_info(self, device_path: str) -> Optional[str]:
        """
        获取USB接口号信息
        
        Args:
            device_path: 设备路径，如 /dev/sdb
            
        Returns:
            USB接口号信息，如 "Bus 003 Device 010"
        """
        try:
            import subprocess
            
            # 通过lsblk获取设备的USB信息
            result = subprocess.run(['lsblk', '-o', 'NAME,TRAN,SERIAL', '-n', device_path], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if 'usb' in line.lower():
                        # 进一步通过lsusb获取详细的USB端口信息
                        lsusb_result = subprocess.run(['lsusb'], capture_output=True, text=True)
                        if lsusb_result.returncode == 0:
                            for usb_line in lsusb_result.stdout.split('\n'):
                                if '2ca3' in usb_line:  # DJI厂商ID
                                    # 提取Bus和Device信息
                                    parts = usb_line.split()
                                    if len(parts) >= 4:
                                        bus_info = f"{parts[1]} {parts[3]}"  # Bus XXX Device XXX
                                        return bus_info
            
            # 备用方案：通过udevadm获取USB信息
            result = subprocess.run(['udevadm', 'info', '--query=all', f'--name={device_path}'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'ID_PATH' in line and 'usb' in line:
                        # 从路径中提取USB端口信息
                        path_info = line.split('=')[1] if '=' in line else ''
                        if path_info:
                            return f"USB_PATH: {path_info}"
            
            return "USB端口信息未知"
            
        except Exception as e:
            self.logger.error(f"获取USB端口信息失败: {e}")
            return "USB端口信息获取失败"

    def report_device_connected(self, device_serial: str, device_path: str, 
                              mount_point: Optional[str] = None, 
                              additional_info: Optional[Dict[str, Any]] = None):
        """
        上报设备连接事件
        
        Args:
            device_serial: 设备序列号/SN号
            device_path: 设备路径，如 /dev/sdb
            mount_point: 挂载点路径
            additional_info: 额外信息
        """
        with self.report_lock:
            try:
                # 获取USB接口号
                usb_port = self.get_usb_port_info(device_path)
                
                # 构建上报信息
                report_data = {
                    "event_type": "DEVICE_CONNECTED",
                    "timestamp": datetime.now().isoformat(),
                    "device_info": {
                        "serial_number": device_serial,
                        "device_path": device_path,
                        "usb_port": usb_port,
                        "mount_point": mount_point
                    }
                }
                
                # 添加额外信息
                if additional_info:
                    report_data["additional_info"] = additional_info
                
                # 记录上报信息
                self.logger.info("=" * 60)
                self.logger.info("DJI设备连接事件上报")
                self.logger.info("=" * 60)
                self.logger.info(f"设备序列号(SN): {device_serial}")
                self.logger.info(f"设备路径: {device_path}")
                self.logger.info(f"USB接口号: {usb_port}")
                self.logger.info(f"挂载点: {mount_point or '未挂载'}")
                self.logger.info(f"上报时间: {report_data['timestamp']}")
                
                if additional_info:
                    self.logger.info("额外信息:")
                    for key, value in additional_info.items():
                        self.logger.info(f"  {key}: {value}")
                
                # 这里可以添加实际的上报逻辑，比如发送HTTP请求到服务器
                # 目前只在日志中记录
                self.logger.info("设备连接事件上报完成")
                self.logger.info("=" * 60)
                
                return True
                
            except Exception as e:
                self.logger.error(f"上报设备连接事件失败: {e}")
                return False

    def report_device_disconnected(self, device_serial: str, device_path: str,
                                 mount_point: Optional[str] = None,
                                 additional_info: Optional[Dict[str, Any]] = None):
        """
        上报设备断开事件
        
        Args:
            device_serial: 设备序列号/SN号
            device_path: 设备路径，如 /dev/sdb
            mount_point: 挂载点路径
            additional_info: 额外信息
        """
        with self.report_lock:
            try:
                # 获取USB接口号（设备已断开，可能获取不到）
                usb_port = self.get_usb_port_info(device_path) if device_path else "设备已断开"
                
                # 构建上报信息
                report_data = {
                    "event_type": "DEVICE_DISCONNECTED",
                    "timestamp": datetime.now().isoformat(),
                    "device_info": {
                        "serial_number": device_serial,
                        "device_path": device_path,
                        "usb_port": usb_port,
                        "mount_point": mount_point
                    }
                }
                
                # 添加额外信息
                if additional_info:
                    report_data["additional_info"] = additional_info
                
                # 记录上报信息
                self.logger.info("=" * 60)
                self.logger.info("DJI设备断开事件上报")
                self.logger.info("=" * 60)
                self.logger.info(f"设备序列号(SN): {device_serial}")
                self.logger.info(f"设备路径: {device_path or '未知'}")
                self.logger.info(f"USB接口号: {usb_port}")
                self.logger.info(f"挂载点: {mount_point or '未挂载'}")
                self.logger.info(f"上报时间: {report_data['timestamp']}")
                
                if additional_info:
                    self.logger.info("额外信息:")
                    for key, value in additional_info.items():
                        self.logger.info(f"  {key}: {value}")
                
                # 这里可以添加实际的上报逻辑，比如发送HTTP请求到服务器
                # 目前只在日志中记录
                self.logger.info("设备断开事件上报完成")
                self.logger.info("=" * 60)
                
                return True
                
            except Exception as e:
                self.logger.error(f"上报设备断开事件失败: {e}")
                return False

    def report_upload_completed(self, device_serial: str, device_path: str,
                              upload_stats: Dict[str, Any],
                              additional_info: Optional[Dict[str, Any]] = None):
        """
        上报文件上传完成事件
        
        Args:
            device_serial: 设备序列号/SN号
            device_path: 设备路径，如 /dev/sdb
            upload_stats: 上传统计信息
            additional_info: 额外信息
        """
        with self.report_lock:
            try:
                # 获取USB接口号
                usb_port = self.get_usb_port_info(device_path)
                
                # 构建上报信息
                report_data = {
                    "event_type": "UPLOAD_COMPLETED",
                    "timestamp": datetime.now().isoformat(),
                    "device_info": {
                        "serial_number": device_serial,
                        "device_path": device_path,
                        "usb_port": usb_port
                    },
                    "upload_stats": upload_stats
                }
                
                # 添加额外信息
                if additional_info:
                    report_data["additional_info"] = additional_info
                
                # 记录上报信息
                self.logger.info("=" * 60)
                self.logger.info("DJI设备文件上传完成事件上报")
                self.logger.info("=" * 60)
                self.logger.info(f"设备序列号(SN): {device_serial}")
                self.logger.info(f"设备路径: {device_path}")
                self.logger.info(f"USB接口号: {usb_port}")
                self.logger.info(f"上报时间: {report_data['timestamp']}")
                
                # 显示上传统计信息
                self.logger.info("上传统计信息:")
                for key, value in upload_stats.items():
                    if key in ['total_size', 'uploaded_size']:
                        # 格式化文件大小
                        formatted_size = self.format_size(value) if isinstance(value, (int, float)) else value
                        self.logger.info(f"  {key}: {formatted_size}")
                    else:
                        self.logger.info(f"  {key}: {value}")
                
                if additional_info:
                    self.logger.info("额外信息:")
                    for key, value in additional_info.items():
                        self.logger.info(f"  {key}: {value}")
                
                # 这里可以添加实际的上报逻辑，比如发送HTTP请求到服务器
                # 目前只在日志中记录
                self.logger.info("文件上传完成事件上报完成")
                self.logger.info("=" * 60)
                
                return True
                
            except Exception as e:
                self.logger.error(f"上报文件上传完成事件失败: {e}")
                return False

    def format_size(self, size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            格式化后的大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"

    def report_custom_event(self, event_type: str, device_serial: str, 
                          device_path: str, event_data: Dict[str, Any]):
        """
        上报自定义事件
        
        Args:
            event_type: 事件类型
            device_serial: 设备序列号/SN号
            device_path: 设备路径
            event_data: 事件数据
        """
        with self.report_lock:
            try:
                # 获取USB接口号
                usb_port = self.get_usb_port_info(device_path)
                
                # 构建上报信息
                report_data = {
                    "event_type": event_type,
                    "timestamp": datetime.now().isoformat(),
                    "device_info": {
                        "serial_number": device_serial,
                        "device_path": device_path,
                        "usb_port": usb_port
                    },
                    "event_data": event_data
                }
                
                # 记录上报信息
                self.logger.info("=" * 60)
                self.logger.info(f"DJI设备自定义事件上报: {event_type}")
                self.logger.info("=" * 60)
                self.logger.info(f"设备序列号(SN): {device_serial}")
                self.logger.info(f"设备路径: {device_path}")
                self.logger.info(f"USB接口号: {usb_port}")
                self.logger.info(f"上报时间: {report_data['timestamp']}")
                
                # 显示事件数据
                self.logger.info("事件数据:")
                for key, value in event_data.items():
                    self.logger.info(f"  {key}: {value}")
                
                # 这里可以添加实际的上报逻辑
                self.logger.info(f"自定义事件 {event_type} 上报完成")
                self.logger.info("=" * 60)
                
                return True
                
            except Exception as e:
                self.logger.error(f"上报自定义事件失败: {e}")
                return False


# 全局上报服务实例
_report_service = None

def get_report_service() -> DJIReportService:
    """获取全局上报服务实例"""
    global _report_service
    if _report_service is None:
        _report_service = DJIReportService()
    return _report_service


# 便捷函数
def report_device_connected(device_serial: str, device_path: str, 
                          mount_point: Optional[str] = None, 
                          additional_info: Optional[Dict[str, Any]] = None) -> bool:
    """上报设备连接事件的便捷函数"""
    return get_report_service().report_device_connected(
        device_serial, device_path, mount_point, additional_info
    )


def report_device_disconnected(device_serial: str, device_path: str,
                             mount_point: Optional[str] = None,
                             additional_info: Optional[Dict[str, Any]] = None) -> bool:
    """上报设备断开事件的便捷函数"""
    return get_report_service().report_device_disconnected(
        device_serial, device_path, mount_point, additional_info
    )


def report_upload_completed(device_serial: str, device_path: str,
                          upload_stats: Dict[str, Any],
                          additional_info: Optional[Dict[str, Any]] = None) -> bool:
    """上报文件上传完成事件的便捷函数"""
    return get_report_service().report_upload_completed(
        device_serial, device_path, upload_stats, additional_info
    )


if __name__ == "__main__":
    # 测试代码
    service = DJIReportService()
    
    # 测试设备连接上报
    service.report_device_connected(
        device_serial="DJI-1581F87L524CU00SZ0K7",
        device_path="/dev/sdb",
        mount_point="/media/dji_device_123456",
        additional_info={"device_type": "DJI Mini 3", "firmware_version": "1.0.0"}
    )
    
    # 测试上传完成上报
    upload_stats = {
        "total_files": 25,
        "uploaded_files": 25,
        "failed_files": 0,
        "total_size": **********,  # 2GB
        "uploaded_size": **********,
        "upload_duration": 300  # 5分钟
    }
    
    service.report_upload_completed(
        device_serial="DJI-1581F87L524CU00SZ0K7",
        device_path="/dev/sdb",
        upload_stats=upload_stats,
        additional_info={"cloud_provider": "aliyun_oss", "backup_location": "/home/<USER>/DJI-1581F87L524CU00SZ0K7"}
    )
    
    # 测试设备断开上报
    service.report_device_disconnected(
        device_serial="DJI-1581F87L524CU00SZ0K7",
        device_path="/dev/sdb",
        mount_point="/media/dji_device_123456",
        additional_info={"disconnect_reason": "user_removed"}
    )
