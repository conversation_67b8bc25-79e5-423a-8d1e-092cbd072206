[Unit]
Description=DJI Device Monitor Service
Documentation=DJI设备专用监控服务
After=multi-user.target
Wants=multi-user.target

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/bin/python3 /opt/dji_monitor/dji_monitor.py
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 防止服务被杀死的配置
OOMScoreAdjust=-1000
KillMode=process

# 环境变量
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target

